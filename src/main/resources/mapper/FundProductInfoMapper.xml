<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.icarus.bi.mapper.FundProductInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.icarus.bi.entity.FundProductInfo">
        <id column="id" property="id" />
        <result column="task_id" property="taskId" />
        <result column="fee_upper_limit" property="feeUpperLimit" />
        <result column="redemption_fee_description" property="redemptionFeeDescription" />
        <result column="fee_lower_limit" property="feeLowerLimit" />
        <result column="fee_rate" property="feeRate" />
        <result column="iopv_provider" property="iopvProvider" />
        <result column="share_type_scope" property="shareTypeScope" />
        <result column="redemption_fee_rate" property="redemptionFeeRate" />
        <result column="investment_scope" property="investmentScope" />
        <result column="upper_limit_symbol" property="upperLimitSymbol" />
        <result column="benchmark" property="benchmark" />
        <result column="product_type" property="productType" />
        <result column="custodian_fee_description" property="custodianFeeDescription" />
        <result column="custodian_fee_rate" property="custodianFeeRate" />
        <result column="alert_scale" property="alertScale" />
        <result column="fund_issuance_start_date" property="fundIssuanceStartDate" />
        <result column="product_share_quantity" property="productShareQuantity" />
        <result column="share_type" property="shareType" />
        <result column="management_fee_rate" property="managementFeeRate" />
        <result column="lower_limit_symbol" property="lowerLimitSymbol" />
        <result column="fund_issuance_end_date" property="fundIssuanceEndDate" />
        <result column="product_name" property="productName" />
        <result column="subscription_fee_description" property="subscriptionFeeDescription" />
        <result column="sales_service_fee_rate" property="salesServiceFeeRate" />
        <result column="listing_location" property="listingLocation" />
        <result column="holders_meeting_items" property="holdersMeetingItems" />
        <result column="sales_service_fee_description" property="salesServiceFeeDescription" />
        <result column="offering_period" property="offeringPeriod" />
        <result column="fund_custodian" property="fundCustodian" />
        <result column="issue_approval_date" property="issueApprovalDate" />
        <result column="is_initiated_fund" property="isInitiatedFund" />
        <result column="management_fee_description" property="managementFeeDescription" />
        <result column="fee_type" property="feeType" />
        <result column="purchase_fee_description" property="purchaseFeeDescription" />
        <result column="fund_redemption_fund_settlement_date" property="fundRedemptionFundSettlementDate" />
        <result column="product_investment_type" property="productInvestmentType" />
        <result column="issue_approval_description" property="issueApprovalDescription" />
        <result column="min_offering_amount" property="minOfferingAmount" />
        <result column="purchase_fee_rate" property="purchaseFeeRate" />
        <result column="subscription_fee_rate" property="subscriptionFeeRate" />
        <result column="created_time" property="createdTime" />
        <result column="updated_time" property="updatedTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_id, fee_upper_limit, redemption_fee_description, fee_lower_limit, fee_rate, 
        iopv_provider, share_type_scope, redemption_fee_rate, investment_scope, upper_limit_symbol, 
        benchmark, product_type, custodian_fee_description, custodian_fee_rate, alert_scale, 
        fund_issuance_start_date, product_share_quantity, share_type, management_fee_rate, 
        lower_limit_symbol, fund_issuance_end_date, product_name, subscription_fee_description, 
        sales_service_fee_rate, listing_location, holders_meeting_items, sales_service_fee_description, 
        offering_period, fund_custodian, issue_approval_date, is_initiated_fund, management_fee_description, 
        fee_type, purchase_fee_description, fund_redemption_fund_settlement_date, product_investment_type, 
        issue_approval_description, min_offering_amount, purchase_fee_rate, subscription_fee_rate, 
        created_time, updated_time
    </sql>

    <!-- 根据任务ID查询基金产品信息列表 -->
    <select id="selectByTaskId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM fund_product_info
        WHERE task_id = #{taskId}
        ORDER BY created_time DESC
    </select>

    <!-- 根据产品名称模糊查询 -->
    <select id="selectByProductNameLike" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM fund_product_info
        WHERE product_name LIKE CONCAT('%', #{productName}, '%')
        ORDER BY created_time DESC
    </select>

    <!-- 根据产品类型查询 -->
    <select id="selectByProductType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM fund_product_info
        WHERE product_type = #{productType}
        ORDER BY created_time DESC
    </select>

    <!-- 根据任务ID删除基金产品信息 -->
    <delete id="deleteByTaskId">
        DELETE FROM fund_product_info WHERE task_id = #{taskId}
    </delete>

    <!-- 批量插入基金产品信息 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO fund_product_info (
            task_id, fee_upper_limit, redemption_fee_description, fee_lower_limit, fee_rate,
            iopv_provider, share_type_scope, redemption_fee_rate, investment_scope, upper_limit_symbol,
            benchmark, product_type, custodian_fee_description, custodian_fee_rate, alert_scale,
            fund_issuance_start_date, product_share_quantity, share_type, management_fee_rate,
            lower_limit_symbol, fund_issuance_end_date, product_name, subscription_fee_description,
            sales_service_fee_rate, listing_location, holders_meeting_items, sales_service_fee_description,
            offering_period, fund_custodian, issue_approval_date, is_initiated_fund, management_fee_description,
            fee_type, purchase_fee_description, fund_redemption_fund_settlement_date, product_investment_type,
            issue_approval_description, min_offering_amount, purchase_fee_rate, subscription_fee_rate,
            created_time, updated_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.taskId}, #{item.feeUpperLimit}, #{item.redemptionFeeDescription}, #{item.feeLowerLimit}, #{item.feeRate},
                #{item.iopvProvider}, #{item.shareTypeScope}, #{item.redemptionFeeRate}, #{item.investmentScope}, #{item.upperLimitSymbol},
                #{item.benchmark}, #{item.productType}, #{item.custodianFeeDescription}, #{item.custodianFeeRate}, #{item.alertScale},
                #{item.fundIssuanceStartDate}, #{item.productShareQuantity}, #{item.shareType}, #{item.managementFeeRate},
                #{item.lowerLimitSymbol}, #{item.fundIssuanceEndDate}, #{item.productName}, #{item.subscriptionFeeDescription},
                #{item.salesServiceFeeRate}, #{item.listingLocation}, #{item.holdersMeetingItems}, #{item.salesServiceFeeDescription},
                #{item.offeringPeriod}, #{item.fundCustodian}, #{item.issueApprovalDate}, #{item.isInitiatedFund}, #{item.managementFeeDescription},
                #{item.feeType}, #{item.purchaseFeeDescription}, #{item.fundRedemptionFundSettlementDate}, #{item.productInvestmentType},
                #{item.issueApprovalDescription}, #{item.minOfferingAmount}, #{item.purchaseFeeRate}, #{item.subscriptionFeeRate},
                #{item.createdTime}, #{item.updatedTime}
            )
        </foreach>
    </insert>

</mapper>
