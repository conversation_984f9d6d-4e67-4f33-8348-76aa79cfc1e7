server:
  port: 9122
  tomcat:
    connection-timeout: 1200000
  servlet:
    context-path: /aip-bi-platform

host-ip: **********

spring:
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  datasource:
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${host-ip}:54333/aip_bi_platform
    username: pgvector
    password: pgvector

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
springdoc:
  api-docs:
    enabled: true                  # 启用/禁用API文档的访问
    path: /v3/api-docs            # 设置API文档的访问路径
  swagger-ui:
    path: /swagger-ui.html        # 设置Swagger UI的访问路径
    disable-swagger-default-url: true
    display-request-duration: true # 显示请求持续时间
  packages-to-scan: com.icarus.bi.controller # 指定要扫描的包

minio:
  endpoint: http://${host-ip}:9000
  bucket-name: bi-minio
  agent-bucket-name: dev-minio
  access-key: admin
  secret-key: admin123456

app-configs:
  aip:
    url: http://${host-ip}:9120
    basePath: /aip/api/bi

file:
  linuxBasePath: /home/<USER>/file/
  crawlFilePath: /home/<USER>
yss:
  crawl:
    url: http://*************:7001
