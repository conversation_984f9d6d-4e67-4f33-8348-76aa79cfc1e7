package com.icarus.bi.schedule;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.icarus.bi.entity.BiCotTask;
import com.icarus.bi.enums.TaskErrorReasonEnum;
import com.icarus.bi.enums.TaskStatusEnum;
import com.icarus.bi.service.IBiCotTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;


/**
 * <p>
 * COT任务定时调度器
 * 扫描待执行的抽取任务并提交到线程池执行
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Slf4j
@Component
public class BiCotTaskScheduler {

    @Resource
    private IBiCotTaskService biCotTaskService;

    @Resource
    private IBiCotTaskScheduleService scheduleService;

    @Resource
    private ApplicationContext applicationContext;


    /**
     * 定时扫描待执行的COT任务
     * 每30秒执行一次
     */
    @Scheduled(fixedRate = 30000)
    public void scanAndExecuteTasks() {
        try {
            log.debug("[定时扫描COT任务]开始扫描待执行任务");
            
            // 1. 查询所有待启动状态的任务
            LambdaQueryWrapper<BiCotTask> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BiCotTask::getTaskStatus, TaskStatusEnum.TODO.getCode());
            queryWrapper.orderByAsc(BiCotTask::getCreateTime);
            
            List<BiCotTask> todoTasks = biCotTaskService.list(queryWrapper);
            
            if (todoTasks.isEmpty()) {
                log.debug("[定时扫描COT任务]没有待执行的任务");
                return;
            }
            
            log.info("[定时扫描COT任务]发现待执行任务数量: {}", todoTasks.size());
            
            // 2. 逐个提交任务到线程池执行
            for (BiCotTask task : todoTasks) {
                try {
                    // 获取线程池状态
                    ThreadPoolExecutor executor = (ThreadPoolExecutor) applicationContext.getBean("taskExecutorPool");
                    log.info("[提交前]线程池状态 - 核心:{}, 最大:{}, 当前:{}, 活跃:{}, 队列:{}, taskId:{}",
                            executor.getCorePoolSize(), executor.getMaximumPoolSize(), executor.getPoolSize(),
                            executor.getActiveCount(), executor.getQueue().size(), task.getId());

                    // 提交到线程池异步执行
                    scheduleService.executeTaskAsync(task);
                    biCotTaskService.updateTaskStatus(task.getId(), TaskStatusEnum.WAITING.getCode(), StrUtil.EMPTY);

                    log.info("[提交后]线程池状态 - 当前:{}, 活跃:{}, 队列:{}",
                            executor.getPoolSize(), executor.getActiveCount(), executor.getQueue().size());
                    
                } catch (Exception e) {
                    log.error("[定时扫描COT任务]提交任务失败，不做任何操作，等待下次定时扫描, taskId:{}, error:{}", task.getId(), e.getMessage(), e);
                    // 如果提交失败，等待下次扫描
                }
            }
            
            log.info("[定时扫描COT任务]任务提交完成, 提交数量: {}", todoTasks.size());
            
        } catch (Exception e) {
            log.error("[定时扫描COT任务]扫描过程发生异常, error:{}", e.getMessage(), e);
        }
    }

    /**
     * 定时清理长时间运行的任务
     * 每5分钟执行一次，检查超过30分钟还在执行中状态的任务
     * 执行中状态包括：COT_DOING、COT_DONE、IDX_DOING
     */
    @Scheduled(fixedRate = 300000)
    public void cleanupLongRunningTasks() {
        try {
            log.debug("[定时清理长时间运行任务]开始检查");

            // 查询所有执行中状态超过30分钟的任务
            LambdaQueryWrapper<BiCotTask> queryWrapper = new LambdaQueryWrapper<>();
            // 使用枚举方法获取所有执行中的状态
            queryWrapper.in(BiCotTask::getTaskStatus, TaskStatusEnum.getRunningStatusCodes());
            // 任务开始时间超过30分钟
            queryWrapper.lt(BiCotTask::getTaskStartTime,
                    java.time.LocalDateTime.now().minusMinutes(30));
            
            List<BiCotTask> longRunningTasks = biCotTaskService.list(queryWrapper);
            
            if (!longRunningTasks.isEmpty()) {
                log.warn("[定时清理长时间运行任务]发现长时间运行任务数量: {}", longRunningTasks.size());
                
                for (BiCotTask task : longRunningTasks) {
                    try {
                        log.warn("[定时清理长时间运行任务]清理任务, taskId:{}, 开始时间:{}", 
                                task.getId(), task.getTaskStartTime());
                        
                        // 将任务状态设置为错误
                        biCotTaskService.updateTaskStatus(task.getId(), TaskStatusEnum.ERROR.getCode(), TaskErrorReasonEnum.TASK_TIMEOUT.getCode());
                        log.info("[定时清理长时间运行任务]任务执行超时,更新 taskId:{} 的任务状态为【ERROR】", task.getId());
                        
                    } catch (Exception e) {
                        log.error("[定时清理长时间运行任务]清理任务失败, taskId:{}, error:{}", 
                                task.getId(), e.getMessage());
                    }
                }
            } else {
                log.debug("[定时清理长时间运行任务]没有发现长时间运行的任务");
            }
            
        } catch (Exception e) {
            log.error("[定时清理长时间运行任务]清理过程发生异常, error:{}", e.getMessage(), e);
        }
    }
}
