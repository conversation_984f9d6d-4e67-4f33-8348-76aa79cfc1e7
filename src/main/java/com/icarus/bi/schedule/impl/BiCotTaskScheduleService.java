package com.icarus.bi.schedule.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.icarus.bi.entity.BiCotResult;
import com.icarus.bi.entity.BiCotRule;
import com.icarus.bi.entity.BiCotTask;
import com.icarus.bi.entity.BiIdxObj;
import com.icarus.bi.enums.TaskStatusEnum;
import com.icarus.bi.schedule.IBiCotTaskScheduleService;
import com.icarus.bi.service.IBiCotResultService;
import com.icarus.bi.service.IBiCotRuleService;
import com.icarus.bi.service.IBiCotTaskService;
import com.icarus.bi.service.biz.BiCotResultBizService;
import com.icarus.bi.service.biz.BiIdxDataBizService;
import com.icarus.bi.service.biz.BiIdxObjBizService;
import com.icarus.bi.service.external.AgentCotService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * COT任务定时执行服务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Slf4j
@Service
public class BiCotTaskScheduleService implements IBiCotTaskScheduleService {

    @Resource
    private IBiCotTaskService biCotTaskService;

    @Resource
    private IBiCotRuleService biCotRuleService;

    @Resource
    private IBiCotResultService biCotResultService;

    @Resource
    private BiIdxDataBizService biIdxDataBizService;

    @Resource
    private AgentCotService agentCotService;

    @Resource
    private BiCotResultBizService biCotResultBizService;

    @Resource
    private BiIdxObjBizService idxObjBizService;

    /**
     * 异步执行单个COT任务
     * 
     * @param task COT任务
     */
    @Override
    @Async("taskExecutorPool")
    public void executeTaskAsync(BiCotTask task) {
        String taskId = task.getId();
        String ruleId = task.getRuleId();
        
        log.info("[异步执行COT任务]开始, taskId:{}, ruleId:{}", taskId, ruleId);
        
        try {
            // 1. 更新任务状态为执行中
            biCotTaskService.updateTaskStatus(taskId, TaskStatusEnum.COT_DOING.getCode(), StrUtil.EMPTY);
            
            // 2. 获取抽取规则配置
            BiCotRule rule = biCotRuleService.getRuleById(ruleId);
            if (rule == null) {
                log.error("[异步执行COT任务]未找到抽取规则,更新任务状态为【ERROR】 taskId:{}, ruleId:{}", taskId, ruleId);
                biCotTaskService.updateTaskStatus(taskId, TaskStatusEnum.ERROR.getCode(), "未找到rule抽取规则");
                return;
            }
            
            // 3. 调用Agent平台COT接口抽取要素,并存储到bi_cot_result表
            String canvasId = rule.getCanvasId();
            String extractResult = agentCotService.extractElements(canvasId,task.getSourceFileKey());

            // 4. 解析抽取结果
            Map<String, Object> extractElements = agentCotService.parseExtractResult(extractResult);

            if (CollectionUtil.isEmpty(extractElements)){
                log.error("[异步执行COT任务]解析抽取结果失败, taskId:{},更新任务状态为【ERROR】", taskId);
                biCotTaskService.updateTaskStatus(taskId, TaskStatusEnum.ERROR.getCode(), "解析抽取结果失败");
                return;
            }

            // 5. 保存抽取结果到bi_cot_result表
            List<BiCotResult> extractResults = biCotResultBizService.batchSaveResults(taskId, ruleId, extractElements);

            // 6. 更新任务状态为完成
            biCotTaskService.updateTaskStatus(taskId, TaskStatusEnum.COT_DONE.getCode(), StrUtil.EMPTY);

            // 7. 根据抽取结果生成obj数据

            log.info("[异步执行COT任务]生成obj数据成功, taskId:{}, ruleId:{}", taskId, ruleId);
            BiIdxObj objFromResult = idxObjBizService.createObjFromResult(taskId, ruleId, extractResults);

            if (ObjectUtil.isNull(objFromResult)) {
                log.error("[异步执行COT任务]生成obj数据失败, taskId:{}, 更新任务状态为【ERROR】", taskId);
                biCotTaskService.updateTaskStatus(taskId, TaskStatusEnum.ERROR.getCode());
                return;
            }

            // 8. 生成指标数据
            boolean generateIdxSuccess = biIdxDataBizService.generateIdxDataFromResults(objFromResult.getId(), rule.getId(), extractResults);

            if (!generateIdxSuccess) {
                log.error("[异步执行COT任务]生成指标数据失败, taskId:{}, 更新任务状态为【ERROR】", taskId);
                biCotTaskService.updateTaskStatus(taskId, TaskStatusEnum.ERROR.getCode());
                return;
            }

            biCotTaskService.updateTaskStatus(taskId, TaskStatusEnum.IDX_DONE.getCode());

            log.info("[异步执行COT任务]执行成功, taskId:{}, 抽取要素数量:{}", taskId, extractElements.size());
            
        } catch (Exception e) {
            log.error("[异步执行COT任务]执行失败, taskId:{}, error:{}", taskId, e.getMessage(), e);
            handleTaskFailure(taskId, e.getMessage());
        }
    }

    /**
     * 处理任务执行失败的情况
     */
    private void handleTaskFailure(String taskId, String errorMessage) {
        try {
            log.warn("[处理任务失败]taskId:{}, error:{}, 更新任务状态为【ERROR】", taskId, errorMessage);
            
            // 清理可能已保存的部分数据
            biCotResultService.deleteResultsByTaskId(taskId);
//            biIdxDataBizService.deleteIdxDataByTaskId(taskId);
            
            // 更新任务状态
            biCotTaskService.updateTaskStatus(taskId, TaskStatusEnum.ERROR.getCode());
            
        } catch (Exception e) {
            log.error("[处理任务失败]清理失败, taskId:{}, error:{}", taskId, e.getMessage(), e);
        }
    }
}
