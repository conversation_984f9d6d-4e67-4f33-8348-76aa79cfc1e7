package com.icarus.bi.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

/**
 * <p>
 * 定时任务配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Slf4j
@Configuration
@EnableScheduling
public class ScheduleConfig {

    /**
     * 定时任务线程池
     */
    @Bean("cotTaskExecutor")
    public TaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        // 核心线程数
        scheduler.setPoolSize(3);
        // 线程名前缀
        scheduler.setThreadNamePrefix("Schedule-");
        // 等待任务完成后关闭
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        // 等待时间
        scheduler.setAwaitTerminationSeconds(60);
        scheduler.initialize();
        
        log.info("初始化定时任务线程池完成 - 核心线程数: {}", scheduler.getPoolSize());
        
        return scheduler;
    }
}
