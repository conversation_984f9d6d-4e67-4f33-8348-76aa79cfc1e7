package com.icarus.bi.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

@Configuration
@Slf4j
public class ThreadPoolConfig {
    
    @Value("${thread.pool.core-size:3}")
    private int corePoolSize;
    
    @Value("${thread.pool.max-size:3}")
    private int maxPoolSize;
    
    @Value("${thread.pool.queue-capacity:80}")
    private int queueCapacity;
    
    @Value("${thread.pool.keep-alive-seconds:300}")
    private int keepAliveSeconds;
    
    @Bean("taskExecutorPool")
    public ThreadPoolExecutor taskExecutorPool() {
        log.info("初始化线程池 - 核心线程数:{}, 最大线程数:{}, 队列容量:{}",
                corePoolSize, maxPoolSize, queueCapacity);

        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                corePoolSize,
                maxPoolSize,
                keepAliveSeconds,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(queueCapacity),
                new ThreadFactory() {
                    private final AtomicInteger threadNumber = new AtomicInteger(1);
                    @Override
                    public Thread newThread(Runnable r) {
                        Thread thread = new Thread(r, "cotTask-executor-" + threadNumber.getAndIncrement());
                        thread.setDaemon(false);
                        thread.setPriority(Thread.NORM_PRIORITY);
                        log.info("创建新线程: {}", thread.getName());
                        return thread;
                    }
                },
                new ThreadPoolExecutor.CallerRunsPolicy() {
                    @Override
                    public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
                        log.info("线程池拒绝任务 - 核心线程数:{}, 最大线程数:{}, 当前线程数:{}, 活跃线程数:{}, 队列大小:{}, 队列剩余容量:{}, 已完成任务数:{}",
                                e.getCorePoolSize(), e.getMaximumPoolSize(), e.getPoolSize(),
                                e.getActiveCount(), e.getQueue().size(), e.getQueue().remainingCapacity(),
                                e.getCompletedTaskCount());
                        log.info("拒绝的任务类型: {}", r.getClass().getName());
                        super.rejectedExecution(r, e);
                    }
                }
        );

        return executor;
    }
} 