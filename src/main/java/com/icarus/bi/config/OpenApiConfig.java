package com.icarus.bi.config;


import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * swagger地址:
 * dev -	http://localhost:9122/swagger-ui/index.html
 */
@Configuration
public class OpenApiConfig {
    @Bean
    public OpenAPI selfOpenAPI() {
        return new OpenAPI().info(new Info()
                .title("问数 API文档")
                .description("应用接口文档")
                .version("v1.0.0"));
    }
}
