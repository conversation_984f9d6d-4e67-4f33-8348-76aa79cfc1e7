package com.icarus.bi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.icarus.bi.anno.ValidationGoups;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
/**
 * <p>
 * 指标数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-16
 */
@Getter
@Setter
@ToString
//todo 换了个临时表，后面需改回来
@TableName("bi_idx_data")
@ApiModel(value = "BiIdxData对象", description = "指标数据表")
public class BiIdxData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    @Null(message = "新建时[ID]必须为空", groups = ValidationGoups.Create.class)
    @NotNull(message = "更新时[ID]不可为空", groups = ValidationGoups.Update.class)
    private String id;

    /**
     * 指标配置ID
     */
    @ApiModelProperty("指标配置ID")
    @NotBlank(message = "[指标配置ID]不能为空")
    private String idxConfId;

    /**
     * 指标code
     */
    @ApiModelProperty("指标code")
    private String idxConfCode;

    /**
     * 指标name
     */
    @ApiModelProperty("指标name")
    private String idxConfName;

    /**
     * 指标配置顺序
     */
    @ApiModelProperty("指标配置顺序")
    private String idxConfOrder;

    /**
     * 描述对象ID
     */
    @ApiModelProperty("描述对象ID")
    private String objId;

    /**
     * 抽取时间
     */
    @ApiModelProperty("抽取时间")
    private LocalDateTime extractionTime;

    /**
     * 抽取规则ID
     */
    @ApiModelProperty("抽取规则ID")
    private String ruleId;

    /**
     * 指标值
     */
    @ApiModelProperty("指标值")
    private String value;

    /**
     * 对应要素字段code
     */
    @ApiModelProperty("对应要素字段code")
    private String elementField;

    /**
     * 记录创建时间
     */
    @ApiModelProperty("记录创建时间")
    private LocalDateTime createTime;

    /**
     * 记录更新时间
     */
    @ApiModelProperty("记录更新时间")
    private LocalDateTime updateTime;

    /**
     * 描述对象名称
     */
    @ApiModelProperty("描述对象名称")
    private String objName;
}
