package com.icarus.bi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
/**
 * <p>
 * 数据源配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@Getter
@Setter
@ToString
@TableName("bi_idx_dsconf")
@ApiModel(value = "BiIdxDsconf对象", description = "数据源配置表")
public class BiIdxDsconf implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据源ID
     */
    @ApiModelProperty("数据源ID")
    private String id;

    /**
     * 数据源名称
     */
    @ApiModelProperty("数据源名称")
    private String name;

    /**
     * 数据源url
     */
    @ApiModelProperty("数据源url")
    private String ip;

    /**
     * 数据源username
     */
    @ApiModelProperty("数据源username")
    private String username;

    /**
     * 数据源password
     */
    @ApiModelProperty("数据源password")
    private String password;

    /**
     * 数据源驱动类型
     */
    @ApiModelProperty("数据源驱动类型")
    private String driverType;

    /**
     * 是否启用
     */
    @ApiModelProperty("是否启用")
    private Boolean ifActive;

    /**
     * 首次接入时间
     */
    @ApiModelProperty("首次接入时间")
    private LocalDateTime accessedTime;

    /**
     * 记录创建时间
     */
    @ApiModelProperty("记录创建时间")
    private LocalDateTime createTime;

    /**
     * 记录上次更新时间
     */
    @ApiModelProperty("记录上次更新时间")
    private LocalDateTime updateTime;

    /**
     * 数据源数据库
     */
    @ApiModelProperty("数据源数据库")
    private String dbname;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String description;

    /**
     * 供应商
     */
    @ApiModelProperty("供应商")
    private String source;

    /**
     * 接入方式(API/PUSH)
     */
    @ApiModelProperty("接入方式(API/PUSH)")
    private String accessMethod;

    /**
     * 最近数据同步时间
     */
    @ApiModelProperty("最近数据同步时间")
    private LocalDateTime lastSyncTime;

    /**
     * 日志路径
     */
    @ApiModelProperty("日志路径")
    private String logPath;
}
