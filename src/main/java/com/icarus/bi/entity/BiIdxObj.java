package com.icarus.bi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
/**
 * <p>
 * 指标描述对象表(当前作为产品表)
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Getter
@Setter
@ToString
@TableName("bi_idx_obj")
@ApiModel(value = "BiIdxObj对象", description = "指标描述对象表(当前作为产品表)")
public class BiIdxObj implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    private String id;

    /**
     * 对象名称(FG场景存储基金产品名称)
     */
    @ApiModelProperty("对象名称(FG场景存储基金产品名称)")
    private String name;

    /**
     * 其他扩展信息JSON
     */
    @ApiModelProperty("其他扩展信息JSON")
    private String extraAttrs;

    /**
     * 所属场景
     */
    @ApiModelProperty("所属场景")
    private String elementScene;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String memo;

    /**
     * 是否启用
     */
    @ApiModelProperty("是否启用")
    private Boolean ifActive;

    /**
     * 记录创建时间
     */
    @ApiModelProperty("记录创建时间")
    private LocalDateTime createTime;

    /**
     * 记录上次更新时间
     */
    @ApiModelProperty("记录上次更新时间")
    private LocalDateTime updateTime;

    /**
     * 对应数据来自的taskId
     */
    @ApiModelProperty("对应数据来自的taskId")
    private String taskId;
}
