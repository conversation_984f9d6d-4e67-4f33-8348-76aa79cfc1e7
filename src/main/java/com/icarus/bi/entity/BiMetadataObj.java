package com.icarus.bi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
/**
 * <p>
 * 问数-元数据对象表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@Getter
@Setter
@ToString
@TableName("bi_metadata_obj")
@ApiModel(value = "BiMetadataObj对象", description = "问数-元数据对象表")
public class BiMetadataObj implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 元数据CODE
     */
    @ApiModelProperty("元数据CODE")
    private String code;

    /**
     * 元数据名称
     */
    @ApiModelProperty("元数据名称")
    private String name;

    /**
     * 对象类型枚举(表-TABLE, 字段-FIELD, 键-KEY, 索引-INDEX)
     */
    @ApiModelProperty("对象类型枚举(表-TABLE, 字段-FIELD, 键-KEY, 索引-INDEX)")
    private String objType;

    /**
     * 对象自定义属性(json)
     */
    @ApiModelProperty("对象自定义属性(json)")
    private String attributes;

    /**
     * 关联ERD的ID
     */
    @ApiModelProperty("关联ERD的ID")
    private Integer erdId;

    /**
     * 对象简单描述
     */
    @ApiModelProperty("对象简单描述")
    private String description;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
}
