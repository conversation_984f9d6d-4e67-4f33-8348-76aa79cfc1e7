package com.icarus.bi.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.icarus.bi.anno.ValidationGoups;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
/**
 * <p>
 * 指标配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-16
 */
@Getter
@Setter
@ToString
@TableName("bi_idx_conf")
@ApiModel(value = "BiIdxConf对象", description = "指标配置表")
public class BiIdxConf implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    @Null(message = "新建时[ID]必须为空", groups = ValidationGoups.Create.class)
    @NotNull(message = "更新时[ID]不可为空", groups = ValidationGoups.Update.class)
    private String id;

    /**
     * 指标code
     */
    @ApiModelProperty("指标code")
    @NotBlank(message = "[指标code]不能为空")
    private String code;

    /**
     * 指标name
     */
    @ApiModelProperty("指标name")
    @NotBlank(message = "[指标name]不能为空")
    private String name;

    /**
     * 指标顺序
     */
    @ApiModelProperty("指标顺序")
    @TableField(value = "\"order\"")
    private Short order;

    /**
     * 是否启用
     */
    @ApiModelProperty("是否启用")
    private Boolean ifActive;

    /**
     * 抽取规则ID
     */
    @ApiModelProperty("抽取规则ID")
    private String ruleId;

    /**
     * 对应要素字段code
     */
    @ApiModelProperty("对应要素字段code")
    private String elementField;

    /**
     * 备注说明
     */
    @ApiModelProperty("备注说明")
    private String remark;

    /**
     * 对应要素所属场景
     */
    @ApiModelProperty("对应要素所在场景")
    private String elementScene;

    /**
     * 对应要素所在范围表达式
     */
    @ApiModelProperty("对应要素所在范围表达式")
    private String elementScopeExp;

    /**
     * 记录创建时间
     */
    @ApiModelProperty("记录创建时间")
    private LocalDateTime createTime;

    /**
     * 记录更新时间
     */
    @ApiModelProperty("记录更新时间")
    private LocalDateTime updateTime;

    /**
     * 是否覆盖
     */
    @ApiModelProperty("是否覆盖")
    private Boolean ifOverwrite;

    /**
     * 是否主键
     */
    @ApiModelProperty("是否主键")
    private Boolean ifPrimaryKey;
}
