package com.icarus.bi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.icarus.bi.anno.ValidationGoups;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.AssertFalse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 问数-别名表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Getter
@Setter
@TableName("bi_alias")
@ApiModel(value = "BiAlias对象", description = "问数-别名表")
public class BiAlias implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("ID")
    @TableId(value = "id", type = IdType.AUTO)
    @Null(message = "新建时[ID]必须为空", groups = ValidationGoups.Create.class)
    @NotNull(message = "更新时[ID]不可为空", groups = ValidationGoups.Update.class)
    private Integer id;

    @ApiModelProperty("全称")
    @NotBlank(message = "[全称]不能为空")
    private String fullName;

    @ApiModelProperty("别名列表json")
    @NotBlank(message = "[别名列表]不能为空")
    private String aliasListJson;

    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否已删除")
    @AssertFalse(message = "[是否已删除]需默认为false")
    private Boolean ifDeleted;

    @ApiModelProperty("描述(保留字段)")
    private String description;

    @ApiModelProperty("所属目录ID(保留字段)")
    private String directoryId;
}
