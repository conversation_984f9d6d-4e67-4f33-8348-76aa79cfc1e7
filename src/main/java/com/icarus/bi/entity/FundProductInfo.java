package com.icarus.bi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 基金产品信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
@Getter
@Setter
@TableName("fund_product_info")
@ApiModel(value = "FundProductInfo对象", description = "基金产品信息表")
public class FundProductInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务ID（64位字符串）
     */
    @ApiModelProperty("任务ID（64位字符串）")
    private String taskId;

    /**
     * 费用上限
     */
    @ApiModelProperty("费用上限")
    private String feeUpperLimit;

    /**
     * 赎回费描述
     */
    @ApiModelProperty("赎回费描述")
    private String redemptionFeeDescription;

    /**
     * 费用下限
     */
    @ApiModelProperty("费用下限")
    private String feeLowerLimit;

    /**
     * 费率
     */
    @ApiModelProperty("费率")
    private String feeRate;

    /**
     * IOPV提供商
     */
    @ApiModelProperty("IOPV提供商")
    private String iopvProvider;

    /**
     * 份额类型范围
     */
    @ApiModelProperty("份额类型范围")
    private String shareTypeScope;

    /**
     * 赎回费率
     */
    @ApiModelProperty("赎回费率")
    private String redemptionFeeRate;

    /**
     * 投资范围
     */
    @ApiModelProperty("投资范围")
    private String investmentScope;

    /**
     * 上限符号
     */
    @ApiModelProperty("上限符号")
    private String upperLimitSymbol;

    /**
     * 基准
     */
    @ApiModelProperty("基准")
    private String benchmark;

    /**
     * 产品类型
     */
    @ApiModelProperty("产品类型")
    private String productType;

    /**
     * 托管费描述
     */
    @ApiModelProperty("托管费描述")
    private String custodianFeeDescription;

    /**
     * 托管费率
     */
    @ApiModelProperty("托管费率")
    private String custodianFeeRate;

    /**
     * 预警规模
     */
    @ApiModelProperty("预警规模")
    private String alertScale;

    /**
     * 基金发行开始日期
     */
    @ApiModelProperty("基金发行开始日期")
    private String fundIssuanceStartDate;

    /**
     * 产品份额数量
     */
    @ApiModelProperty("产品份额数量")
    private String productShareQuantity;

    /**
     * 份额类型
     */
    @ApiModelProperty("份额类型")
    private String shareType;

    /**
     * 管理费率
     */
    @ApiModelProperty("管理费率")
    private String managementFeeRate;

    /**
     * 下限符号
     */
    @ApiModelProperty("下限符号")
    private String lowerLimitSymbol;

    /**
     * 基金发行结束日期
     */
    @ApiModelProperty("基金发行结束日期")
    private String fundIssuanceEndDate;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String productName;

    /**
     * 认购费描述
     */
    @ApiModelProperty("认购费描述")
    private String subscriptionFeeDescription;

    /**
     * 销售服务费率
     */
    @ApiModelProperty("销售服务费率")
    private String salesServiceFeeRate;

    /**
     * 上市地点
     */
    @ApiModelProperty("上市地点")
    private String listingLocation;

    /**
     * 持有人大会议题
     */
    @ApiModelProperty("持有人大会议题")
    private String holdersMeetingItems;

    /**
     * 销售服务费描述
     */
    @ApiModelProperty("销售服务费描述")
    private String salesServiceFeeDescription;

    /**
     * 发行期
     */
    @ApiModelProperty("发行期")
    private String offeringPeriod;

    /**
     * 基金托管人
     */
    @ApiModelProperty("基金托管人")
    private String fundCustodian;

    /**
     * 发行批准日期
     */
    @ApiModelProperty("发行批准日期")
    private String issueApprovalDate;

    /**
     * 是否为发起式基金（是/否）
     */
    @ApiModelProperty("是否为发起式基金（是/否）")
    private String isInitiatedFund;

    /**
     * 管理费描述
     */
    @ApiModelProperty("管理费描述")
    private String managementFeeDescription;

    /**
     * 费用类型
     */
    @ApiModelProperty("费用类型")
    private String feeType;

    /**
     * 申购费描述
     */
    @ApiModelProperty("申购费描述")
    private String purchaseFeeDescription;

    /**
     * 基金赎回资金结算日期(确认日期+n)
     */
    @ApiModelProperty("基金赎回资金结算日期(确认日期+n)")
    private String fundRedemptionFundSettlementDate;

    /**
     * 产品投资类型
     */
    @ApiModelProperty("产品投资类型")
    private String productInvestmentType;

    /**
     * 发行批准描述
     */
    @ApiModelProperty("发行批准描述")
    private String issueApprovalDescription;

    /**
     * 最小发行金额
     */
    @ApiModelProperty("最小发行金额")
    private String minOfferingAmount;

    /**
     * 申购费率
     */
    @ApiModelProperty("申购费率")
    private String purchaseFeeRate;

    /**
     * 认购费率
     */
    @ApiModelProperty("认购费率")
    private String subscriptionFeeRate;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updatedTime;
}
