package com.icarus.bi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
/**
 * <p>
 * 问数-元数据ER图表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@Getter
@Setter
@ToString
@TableName("bi_metadata_erd")
@ApiModel(value = "BiMetadataErd对象", description = "问数-元数据ER图表")
public class BiMetadataErd implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * ER图名
     */
    @ApiModelProperty("ER图名")
    private String name;

    /**
     * ER图内容(存前端渲染对象)
     */
    @ApiModelProperty("ER图内容(存前端渲染对象)")
    private String content;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String description;

    /**
     * 所属用户(保留字段)
     */
    @ApiModelProperty("所属用户(保留字段)")
    private String userId;
}
