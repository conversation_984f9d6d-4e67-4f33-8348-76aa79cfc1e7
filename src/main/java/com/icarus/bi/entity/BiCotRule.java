package com.icarus.bi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.icarus.bi.anno.ValidationGoups;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Null;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * <p>
 * 抽取规则配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Getter
@Setter
@ToString
@TableName("bi_cot_rule")
@ApiModel(value = "BiCotRule对象", description = "抽取规则配置表")
public class BiCotRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Null(message = "新建时[ID]必须为空", groups = ValidationGoups.Create.class)
    @NotBlank(message = "更新时[ID]不可为空", groups = ValidationGoups.Update.class)
    private String id;

    /**
     * 规则Code
     */
    @ApiModelProperty("规则Code")
    private String code;

    /**
     * 规则/画布Name
     */
    @ApiModelProperty("规则/画布Name")
    private String name;

    /**
     * 是否启用
     */
    @ApiModelProperty("是否启用")
    private Boolean ifActive;

    /**
     * 处理器cot画布对应canvasId
     */
    @ApiModelProperty("处理器cot画布对应canvasId")
    @NotBlank(message = "新建时处理器cot画布对应canvasId不能为空", groups = ValidationGoups.Create.class)
    private String canvasId;

    /**
     * 其他扩展字段(可以用来存画布上抽取配置)
     */
    @ApiModelProperty("其他扩展字段(可以用来存画布上抽取配置)")
    private String extraAttrs;

    /**
     * 预留字段: 画布版本号
     */
    @ApiModelProperty("预留字段: 画布版本号")
    private String canvasVersion;

    /**
     * 记录创建时间
     */
    @ApiModelProperty("记录创建时间")
    private LocalDateTime createTime;

    /**
     * 记录更新时间
     */
    @ApiModelProperty("记录更新时间")
    private LocalDateTime updateTime;
}
