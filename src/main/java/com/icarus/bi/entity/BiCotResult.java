package com.icarus.bi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.icarus.bi.anno.ValidationGoups;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Null;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * <p>
 * 抽取规则配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Getter
@Setter
@ToString
@TableName("bi_cot_result")
@ApiModel(value = "BiCotResult对象", description = "抽取规则配置表")
public class BiCotResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("ID")
    @Null(message = "新建时[ID]必须为空", groups = ValidationGoups.Create.class)
    @NotBlank(message = "更新时[ID]不可为空", groups = ValidationGoups.Update.class)
    private String id;

    /**
     * 提取任务ID
     */
    @ApiModelProperty("提取任务ID")
    @NotBlank(message = "[提取任务ID]不能为空")
    private String taskId;

    /**
     * 要素字段code
     */
    @ApiModelProperty("要素字段code")
    @NotBlank(message = "[要素字段code]不能为空")
    private String elementField;

    /**
     * 要素字段Name
     */
    @ApiModelProperty("要素字段Name")
    private String elementName;

    /**
     * 要素提取结果
     */
    @ApiModelProperty("要素提取结果")
    private String elementResult;

    /**
     * 要素提取章节(表达式)
     */
    @ApiModelProperty("要素提取章节(表达式)")
    private String elementScopeExp;

    /**
     * 其他扩展字段(可存CMTT相关语料/文件信息JSON)
     */
    @ApiModelProperty("其他扩展字段(可存CMTT相关语料/文件信息JSON)")
    private String extraAttrs;

    /**
     * 记录创建时间
     */
    @ApiModelProperty("记录创建时间")
    private LocalDateTime createTime;

    /**
     * 记录更新时间
     */
    @ApiModelProperty("记录更新时间")
    private LocalDateTime updateTime;
}
