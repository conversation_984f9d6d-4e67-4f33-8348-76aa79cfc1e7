package com.icarus.bi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.icarus.bi.anno.ValidationGoups;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Null;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * <p>
 * 抽取规则配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-16
 */
@Getter
@Setter
@ToString
@TableName("bi_cot_task")
@ApiModel(value = "BiCotTask对象", description = "抽取规则配置表")
public class BiCotTask implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("ID")
    @Null(message = "新建时[ID]必须为空", groups = ValidationGoups.Create.class)
    @NotBlank(message = "更新时[ID]不可为空", groups = ValidationGoups.Update.class)
    private String id;

    /**
     * 抽取规则ID
     */
    @ApiModelProperty("抽取规则ID")
    private String ruleId;

    /**
     * 抽取规则Name
     */
    @ApiModelProperty("抽取规则Name")
    private String ruleName;

    /**
     * 处理器cot对应canvasId
     */
    @ApiModelProperty("处理器cot对应canvasId")
    private String canvasId;

    /**
     * 其他扩展字段(当前可以存CMTT语料相关信息JSON)
     */
    @ApiModelProperty("其他扩展字段(当前可以存CMTT语料相关信息JSON)")
    private String extraAttrs;

    /**
     * 要素提取源文件key
     */
    @ApiModelProperty("要素提取源文件key")
    private String sourceFileKey;

    /**
     * 对应指标对象ID
     */
    @ApiModelProperty("对应指标对象ID")
    private String objId;

    /**
     * 预留字段: 审核状态(UNDO - 待审核, AUDITED - 已审核)
     */
    @ApiModelProperty("预留字段: 审核状态(UNDO - 待审核, AUDITED - 已审核)")
    private String auditStatus;

    /**
     * 执行状态(TODO - 待启动, WAITING - 等待执行,  COT_DOING - COT进行中, COT_DONE - COT已完成, IDX_DOING - IDX进行中, IDX_DONE - IDX已完成, ERROR - 异常)
     */
    @ApiModelProperty("执行状态(TODO - 待启动, COT_DOING - COT进行中, COT_DONE - COT已完成, IDX_DOING - IDX进行中, IDX_DONE - IDX已完成, ERROR - 异常)")
    private String taskStatus;

    /**
     * 任务执行开始
     */
    @ApiModelProperty("任务执行开始")
    private LocalDateTime taskStartTime;

    /**
     * 任务执行结束
     */
    @ApiModelProperty("任务执行结束")
    private LocalDateTime taskEndTime;

    /**
     * 记录创建时间
     */
    @ApiModelProperty("记录创建时间")
    private LocalDateTime createTime;

    /**
     * 记录更新时间
     */
    @ApiModelProperty("记录更新时间")
    private LocalDateTime updateTime;

    /**
     * 错误原因
     */
    @ApiModelProperty("错误原因")
    private String errorReason;
}
