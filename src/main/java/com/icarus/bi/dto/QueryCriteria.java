package com.icarus.bi.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 查询条件集合实体类
 */
@Data
public class QueryCriteria implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 查询条件列表
     */
    private List<QueryCondition> conditions;
    
    /**
     * 目标字段列表
     */
    private List<String> targets;
    
    /**
     * 推理说明
     */
    private String reasoning;

    /**
     * 产品名称
     */
    private List<String> productName;
}