package com.icarus.bi.dto;

import com.icarus.bi.entity.BiIdxData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;


@Getter
@Setter
@ApiModel(value = "BiIdxDataDto对象", description = "指标数据表")
public class BiIdxDataDto implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty("ID")
    private String id;

    /**
     * 指标配置ID
     */
    @ApiModelProperty("指标配置ID")
    private String idxConfId;

    /**
     * 指标code
     */
    @ApiModelProperty("指标code")
    private String idxConfCode;

    /**
     * 指标配置顺序
     */
    @ApiModelProperty("指标配置顺序")
    private String idxConfOrder;

    /**
     * 文件minioKey
     */
    @ApiModelProperty("文件minioKey")
    private String objId;

    /**
     * 抽取时间
     */
    @ApiModelProperty("抽取时间")
    private LocalDateTime extractionTime;

    /**
     * 抽取规则ID
     */
    @ApiModelProperty("抽取规则ID")
    private String ruleId;

    /**
     * 指标值
     */
    @ApiModelProperty("指标值")
    private String value;

    /**
     * 对应要素字段code
     */
    @ApiModelProperty("对应要素字段code")
    private String elementField;

    public BiIdxDataDto(){
        super();
    }

    public BiIdxDataDto(BiIdxData biIdxData){
        super();
        this.id = biIdxData.getId();
        this.idxConfId = biIdxData.getIdxConfId();
        this.idxConfCode = biIdxData.getIdxConfCode();
        this.idxConfOrder = biIdxData.getIdxConfOrder();
        this.objId = biIdxData.getObjId();
        this.extractionTime = biIdxData.getExtractionTime();
        this.ruleId = biIdxData.getRuleId();
        this.value = biIdxData.getValue();
        this.elementField = biIdxData.getElementField();
    }
}


