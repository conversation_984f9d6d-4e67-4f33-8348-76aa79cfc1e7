package com.icarus.bi.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.time.LocalDateTime;

/**
 * <p>
 * COT任务分页查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-17
 */
@Data
@ApiModel(value = "BiCotTaskQueryDTO", description = "COT任务分页查询参数")
public class BiCotTaskQueryDTO {

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码", example = "1")
    @Min(value = 1, message = "页码不能小于1")
    private Integer currentPage = 1;

    /**
     * 每页大小
     */
    @ApiModelProperty(value = "每页大小", example = "10")
    @Min(value = 1, message = "每页大小不能小于1")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer pageSize = 10;

    /**
     * 规则ID（精确查询）
     */
    @ApiModelProperty(value = "规则ID", example = "rule_123")
    private String ruleId;

    /**
     * 规则名称（模糊查询）
     */
    @ApiModelProperty(value = "规则名称", example = "测试规则")
    private String ruleName;

    /**
     * 画布ID（精确查询）
     */
    @ApiModelProperty(value = "画布ID", example = "canvas_123")
    private String canvasId;

    /**
     * 任务执行状态（精确查询）
     */
    @ApiModelProperty(value = "任务执行状态", example = "TODO", allowableValues = "TODO,COT_DOING,COT_DONE,IDX_DOING,IDX_DONE,ERROR")
    private String taskStatus;

    /**
     * 审核状态（精确查询）
     */
    @ApiModelProperty(value = "审核状态", example = "UNDO", allowableValues = "UNDO,AUDITED")
    private String auditStatus;

    /**
     * 扩展属性（模糊查询JSON内容）
     */
    @ApiModelProperty(value = "扩展属性", example = "minioUrl")
    private String extraAttrs;

    /**
     * 要素提取源文件key（模糊查询）
     */
    @ApiModelProperty(value = "要素提取源文件key", example = "minio/files/document.pdf")
    private String sourceFileKey;

    /**
     * 创建时间开始（包含）
     */
    @ApiModelProperty(value = "创建时间开始", example = "2025-01-01 00:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束（包含）
     */
    @ApiModelProperty(value = "创建时间结束", example = "2025-12-31 23:59:59")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeEnd;

    /**
     * 任务结束时间开始（包含）
     */
    @ApiModelProperty(value = "任务结束时间开始", example = "2025-01-01 00:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime taskEndTimeStart;

    /**
     * 任务结束时间结束（包含）
     */
    @ApiModelProperty(value = "任务结束时间结束", example = "2025-12-31 23:59:59")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime taskEndTimeEnd;

    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段", example = "createTime", allowableValues = "createTime,taskEndTime,taskStatus,auditStatus")
    private String orderBy = "createTime";

    /**
     * 排序方向
     */
    @ApiModelProperty(value = "排序方向", example = "desc", allowableValues = "asc,desc")
    private String orderDirection = "desc";

    /**
     * 验证时间范围是否合理
     */
    public boolean isValidTimeRange() {
        // 验证创建时间范围
        if (createTimeStart != null && createTimeEnd != null && createTimeStart.isAfter(createTimeEnd)) {
            return false;
        }
        
        // 验证任务结束时间范围
        if (taskEndTimeStart != null && taskEndTimeEnd != null && taskEndTimeStart.isAfter(taskEndTimeEnd)) {
            return false;
        }
        
        return true;
    }

    /**
     * 获取有效的排序字段
     */
    public String getValidOrderBy() {
        if (orderBy == null || orderBy.trim().isEmpty()) {
            return "createTime";
        }
        
        // 只允许特定字段排序
        switch (orderBy.toLowerCase()) {
            case "createtime":
            case "create_time":
                return "createTime";
            case "taskendtime":
            case "task_end_time":
                return "taskEndTime";
            case "taskstatus":
            case "task_status":
                return "taskStatus";
            case "auditstatus":
            case "audit_status":
                return "auditStatus";
            case "updatetime":
            case "update_time":
                return "updateTime";
            default:
                return "createTime";
        }
    }

    /**
     * 获取有效的排序方向
     */
    public String getValidOrderDirection() {
        if (orderDirection == null || orderDirection.trim().isEmpty()) {
            return "desc";
        }
        
        return "asc".equalsIgnoreCase(orderDirection) ? "asc" : "desc";
    }

    /**
     * 检查是否有查询条件
     */
    public boolean hasQueryConditions() {
        return ruleId != null || ruleName != null || canvasId != null || 
               taskStatus != null || auditStatus != null || extraAttrs != null ||
               createTimeStart != null || createTimeEnd != null ||
               taskEndTimeStart != null || taskEndTimeEnd != null;
    }
}
