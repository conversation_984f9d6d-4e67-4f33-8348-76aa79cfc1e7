package com.icarus.bi.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <p>
 * COT任务错误响应DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "BiCotTaskErrorResponse", description = "COT任务错误响应")
public class BiCotTaskErrorResponse {

    /**
     * 错误码
     */
    @ApiModelProperty("错误码")
    private String errorCode;

    /**
     * 错误消息
     */
    @ApiModelProperty("错误消息")
    private String errorMessage;

    /**
     * 错误时间
     */
    @ApiModelProperty("错误时间")
    private LocalDateTime errorTime;

    /**
     * 建议操作
     */
    @ApiModelProperty("建议操作")
    private String suggestion;

    /**
     * 相关数据
     */
    @ApiModelProperty("相关数据")
    private Object data;

    /**
     * 创建规则未启用错误响应
     */
    public static BiCotTaskErrorResponse ruleNotActive(String message) {
        return BiCotTaskErrorResponse.builder()
                .errorCode("RULE_NOT_ACTIVE")
                .errorMessage(message)
                .errorTime(LocalDateTime.now())
                .suggestion("请先在抽取规则管理页面启用至少一个抽取规则")
                .build();
    }

    /**
     * 创建通用错误响应
     */
    public static BiCotTaskErrorResponse generalError(String errorCode, String message) {
        return BiCotTaskErrorResponse.builder()
                .errorCode(errorCode)
                .errorMessage(message)
                .errorTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建通用错误响应（带建议）
     */
    public static BiCotTaskErrorResponse generalError(String errorCode, String message, String suggestion) {
        return BiCotTaskErrorResponse.builder()
                .errorCode(errorCode)
                .errorMessage(message)
                .errorTime(LocalDateTime.now())
                .suggestion(suggestion)
                .build();
    }
}
