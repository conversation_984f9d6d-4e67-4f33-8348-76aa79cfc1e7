package com.icarus.bi.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.time.LocalDateTime;

/**
 * <p>
 * 抽取规则分页查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-17
 */
@Data
@ApiModel(value = "BiCotRuleQueryDTO", description = "抽取规则分页查询参数")
public class BiCotRuleQueryDTO {

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码", example = "1")
    @Min(value = 1, message = "页码不能小于1")
    private Integer currentPage = 1;

    /**
     * 每页大小
     */
    @ApiModelProperty(value = "每页大小", example = "10")
    @Min(value = 1, message = "每页大小不能小于1")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer pageSize = 10;

    /**
     * 规则Code（模糊查询）
     */
    @ApiModelProperty(value = "规则Code", example = "RULE_001")
    private String code;

    /**
     * 规则/画布Name（模糊查询）
     */
    @ApiModelProperty(value = "规则/画布Name", example = "测试规则")
    private String name;

    /**
     * 画布ID（精确查询）
     */
    @ApiModelProperty(value = "画布ID", example = "canvas_123")
    private String canvasId;

    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用", example = "true")
    private Boolean ifActive;

    /**
     * 画布版本号（精确查询）
     */
    @ApiModelProperty(value = "画布版本号", example = "v1.0")
    private String canvasVersion;

    /**
     * 创建时间开始（包含）
     */
    @ApiModelProperty(value = "创建时间开始", example = "2025-01-01 00:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束（包含）
     */
    @ApiModelProperty(value = "创建时间结束", example = "2025-12-31 23:59:59")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeEnd;

    /**
     * 更新时间开始（包含）
     */
    @ApiModelProperty(value = "更新时间开始", example = "2025-01-01 00:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTimeStart;

    /**
     * 更新时间结束（包含）
     */
    @ApiModelProperty(value = "更新时间结束", example = "2025-12-31 23:59:59")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTimeEnd;

    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段", example = "createTime", allowableValues = "createTime,updateTime,code,name")
    private String orderBy = "createTime";

    /**
     * 排序方向
     */
    @ApiModelProperty(value = "排序方向", example = "desc", allowableValues = "asc,desc")
    private String orderDirection = "desc";

    /**
     * 验证时间范围是否合理
     */
    public boolean isValidTimeRange() {
        // 验证创建时间范围
        if (createTimeStart != null && createTimeEnd != null && createTimeStart.isAfter(createTimeEnd)) {
            return false;
        }
        
        // 验证更新时间范围
        if (updateTimeStart != null && updateTimeEnd != null && updateTimeStart.isAfter(updateTimeEnd)) {
            return false;
        }
        
        return true;
    }

    /**
     * 获取有效的排序字段
     */
    public String getValidOrderBy() {
        if (orderBy == null || orderBy.trim().isEmpty()) {
            return "createTime";
        }
        
        // 只允许特定字段排序
        switch (orderBy.toLowerCase()) {
            case "createtime":
            case "create_time":
                return "createTime";
            case "updatetime":
            case "update_time":
                return "updateTime";
            case "code":
                return "code";
            case "name":
                return "name";
            default:
                return "createTime";
        }
    }

    /**
     * 获取有效的排序方向
     */
    public String getValidOrderDirection() {
        if (orderDirection == null || orderDirection.trim().isEmpty()) {
            return "desc";
        }
        
        return "asc".equalsIgnoreCase(orderDirection) ? "asc" : "desc";
    }
}
