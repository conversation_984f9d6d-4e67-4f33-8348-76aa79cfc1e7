package com.icarus.bi.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import jakarta.validation.Valid;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * BiCotTaskCmttPushReq对象
 *
 * <AUTHOR>
 * @since 2025-08-16
 */
@Getter
@Setter
@ToString
@ApiModel(value = "BiCotTaskCmttPushReq对象", description = "BiCotTaskCmttPushReq对象")
public class BiCotTaskCmttPushReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("ruleId")
    private String ruleId;

    @JsonProperty("detail")
    @Valid
    private DetailDTO detail;
    @JsonProperty("files")
    @Valid
    private List<FilesDTO> files;

    @NoArgsConstructor
    @Data
    public static class DetailDTO {
        @JsonProperty("corpusCollectionConfigId")
        private String corpusCollectionConfigId;
        @JsonProperty("sourceInstitutionUrl")
        private String sourceInstitutionUrl;
        @JsonProperty("subSourceInstitution")
        private String subSourceInstitution;
        @JsonProperty("collectionPath")
        private String collectionPath;
        @JsonProperty("subject")
        private String subject;
        @JsonProperty("publishDate")
        private String publishDate;
        @JsonProperty("description")
        private String description;
        @JsonProperty("officialNumber")
        private String officialNumber;
        @JsonProperty("updateTime")
        private String updateTime;
        @JsonProperty("tags")
        private List<String> tags;
        @JsonProperty("collectionDetailPath")
        private String collectionDetailPath;
        @JsonProperty("sourceEntity")
        private String sourceEntity;
        @JsonProperty("sourceInstitution")
        private String sourceInstitution;
        @JsonProperty("createTime")
        private String createTime;
        @JsonProperty("corpusDatabaseId")
        private String corpusDatabaseId;
        @JsonProperty("name")
        private String name;
        @JsonProperty("id")
        private String id;
        @JsonProperty("collectionPathUrl")
        private String collectionPathUrl;
    }

    @NoArgsConstructor
    @Data
    public static class FilesDTO {
        @JsonProperty("id")
        private String id;
        @JsonProperty("updateTime")
        private String updateTime;
        @JsonProperty("corpusId")
        private String corpusId;
        @JsonProperty("name")
        private String name;
        @JsonProperty("property")
        private String property;
        @JsonProperty("type")
        private String type;
        @JsonProperty("fileSize")
        private String fileSize;
        @JsonProperty("minioFileKey")
        private String minioFileKey;
    }
}
