package com.icarus.bi.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <p>
 * COT任务提交响应DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "BiCotTaskSubmitResponse", description = "COT任务提交响应")
public class BiCotTaskSubmitResponse {

    /**
     * 任务ID
     */
    @ApiModelProperty("任务ID")
    private String taskId;

    /**
     * 抽取规则ID
     */
    @ApiModelProperty("抽取规则ID")
    private String ruleId;

    /**
     * 抽取规则名称
     */
    @ApiModelProperty("抽取规则名称")
    private String ruleName;

    /**
     * 画布ID
     */
    @ApiModelProperty("画布ID")
    private String canvasId;

    /**
     * 执行状态
     */
    @ApiModelProperty("执行状态")
    private String taskStatus;

    /**
     * 审核状态
     */
    @ApiModelProperty("审核状态")
    private String auditStatus;

    /**
     * 提交时间
     */
    @ApiModelProperty("提交时间")
    private LocalDateTime submitTime;

    /**
     * 消息
     */
    @ApiModelProperty("消息")
    private String message;

    /**
     * 创建成功响应
     */
    public static BiCotTaskSubmitResponse success(String taskId, String ruleId, String ruleName, String canvasId) {
        return BiCotTaskSubmitResponse.builder()
                .taskId(taskId)
                .ruleId(ruleId)
                .ruleName(ruleName)
                .canvasId(canvasId)
                .taskStatus("TODO")
                .auditStatus("UNDO")
                .submitTime(LocalDateTime.now())
                .message("任务提交成功，已开始异步处理")
                .build();
    }

    /**
     * 创建失败响应
     */
    public static BiCotTaskSubmitResponse failure(String ruleId, String ruleName, String message) {
        return BiCotTaskSubmitResponse.builder()
                .ruleId(ruleId)
                .ruleName(ruleName)
                .taskStatus("ERROR")
                .submitTime(LocalDateTime.now())
                .message(message)
                .build();
    }
}
