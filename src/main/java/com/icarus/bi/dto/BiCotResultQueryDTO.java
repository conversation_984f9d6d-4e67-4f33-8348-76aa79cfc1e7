package com.icarus.bi.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotBlank;

/**
 * <p>
 * COT抽取结果分页查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-17
 */
@Data
@ApiModel(value = "BiCotResultQueryDTO", description = "COT抽取结果分页查询参数")
public class BiCotResultQueryDTO {

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码", example = "1")
    @Min(value = 1, message = "页码不能小于1")
    private Integer currentPage = 1;

    /**
     * 每页大小
     */
    @ApiModelProperty(value = "每页大小", example = "10")
    @Min(value = 1, message = "每页大小不能小于1")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer pageSize = 10;

    /**
     * 任务ID（必填）
     */
    @ApiModelProperty(value = "任务ID", example = "task_123", required = true)
    @NotBlank(message = "任务ID不能为空")
    private String taskId;

    /**
     * 要素字段code（模糊查询）
     */
    @ApiModelProperty(value = "要素字段code", example = "title")
    private String elementField;

    /**
     * 要素字段Name（模糊查询）
     */
    @ApiModelProperty(value = "要素字段Name", example = "标题")
    private String elementName;

    /**
     * 要素提取结果（模糊查询）
     */
    @ApiModelProperty(value = "要素提取结果", example = "重要")
    private String elementResult;

    /**
     * 要素提取章节表达式（模糊查询）
     */
    @ApiModelProperty(value = "要素提取章节表达式", example = "第一章")
    private String elementScopeExp;

    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段", example = "createTime", allowableValues = "createTime,elementField,elementName")
    private String orderBy = "createTime";

    /**
     * 排序方向
     */
    @ApiModelProperty(value = "排序方向", example = "desc", allowableValues = "asc,desc")
    private String orderDirection = "desc";

    /**
     * 获取有效的排序字段
     */
    public String getValidOrderBy() {
        if (orderBy == null || orderBy.trim().isEmpty()) {
            return "createTime";
        }
        
        // 只允许特定字段排序
        switch (orderBy.toLowerCase()) {
            case "createtime":
            case "create_time":
                return "createTime";
            case "elementfield":
            case "element_field":
                return "elementField";
            case "elementname":
            case "element_name":
                return "elementName";
            case "updatetime":
            case "update_time":
                return "updateTime";
            default:
                return "createTime";
        }
    }

    /**
     * 获取有效的排序方向
     */
    public String getValidOrderDirection() {
        if (orderDirection == null || orderDirection.trim().isEmpty()) {
            return "desc";
        }
        
        return "asc".equalsIgnoreCase(orderDirection) ? "asc" : "desc";
    }

    /**
     * 检查是否有查询条件（除了taskId）
     */
    public boolean hasQueryConditions() {
        return elementField != null || elementName != null || 
               elementResult != null || elementScopeExp != null;
    }
}
