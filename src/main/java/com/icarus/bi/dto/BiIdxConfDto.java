package com.icarus.bi.dto;

import com.icarus.bi.entity.BiIdxConf;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 指标配置表Dto
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Getter
@Setter
@ApiModel(value = "BiIdxConf对象", description = "指标配置表")
public class BiIdxConfDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    private String id;

    /**
     * 指标code
     */
    @ApiModelProperty("指标code")
    private String code;

    /**
     * 指标name
     */
    @ApiModelProperty("指标name")
    private String name;

    /**
     * 指标顺序
     */
    @ApiModelProperty("指标顺序")
    private Short order;

    /**
     * 是否启用
     */
    @ApiModelProperty("是否启用")
    private Boolean ifActive;

    /**
     * 抽取规则ID
     */
    @ApiModelProperty("抽取规则ID")
    private String ruleId;

    /**
     * 对应要素字段code
     */
    @ApiModelProperty("对应要素字段code")
    private String elementField;

    /**
     * 备注说明
     */
    @ApiModelProperty("备注说明")
    private String remark;

    /**
     * 对应要素所属场景
     */
    @ApiModelProperty("对应要素所属场景")
    private String elementScene;

    /**
     * 对应要素所在范围表达式
     */
    @ApiModelProperty("对应要素所在范围表达式")
    private String elementScopeExp;

    public BiIdxConfDto() {
        super();
    }

    public BiIdxConfDto(BiIdxConf biIdxConf){
        super();
        this.id = biIdxConf.getId();
        this.code = biIdxConf.getCode();
        this.name = biIdxConf.getName();
        this.order = biIdxConf.getOrder();
        this.ifActive = biIdxConf.getIfActive();
        this.ruleId = biIdxConf.getRuleId();
        this.elementField = biIdxConf.getElementField();
        this.remark = biIdxConf.getRemark();
        this.elementScene = biIdxConf.getElementScene();
        this.elementScopeExp = biIdxConf.getElementScopeExp();
    }

}
