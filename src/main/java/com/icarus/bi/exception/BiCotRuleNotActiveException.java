package com.icarus.bi.exception;

/**
 * <p>
 * 抽取规则未启用异常
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
public class BiCotRuleNotActiveException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误消息
     */
    private String errorMessage;

    public BiCotRuleNotActiveException() {
        super();
    }

    public BiCotRuleNotActiveException(String message) {
        super(message);
        this.errorMessage = message;
        this.errorCode = "RULE_NOT_ACTIVE";
    }

    public BiCotRuleNotActiveException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.errorMessage = message;
    }

    public BiCotRuleNotActiveException(String message, Throwable cause) {
        super(message, cause);
        this.errorMessage = message;
        this.errorCode = "RULE_NOT_ACTIVE";
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
