package com.icarus.bi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.icarus.bi.entity.FundProductInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 基金产品信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
@Mapper
public interface FundProductInfoMapper extends BaseMapper<FundProductInfo> {

    /**
     * 根据任务ID查询基金产品信息列表
     *
     * @param taskId 任务ID
     * @return 基金产品信息列表
     */
    List<FundProductInfo> selectByTaskId(@Param("taskId") String taskId);

    /**
     * 根据产品名称模糊查询
     *
     * @param productName 产品名称
     * @return 基金产品信息列表
     */
    List<FundProductInfo> selectByProductNameLike(@Param("productName") String productName);

    /**
     * 根据产品类型查询
     *
     * @param productType 产品类型
     * @return 基金产品信息列表
     */
    List<FundProductInfo> selectByProductType(@Param("productType") String productType);

    /**
     * 根据任务ID删除基金产品信息
     *
     * @param taskId 任务ID
     * @return 删除的记录数
     */
    int deleteByTaskId(@Param("taskId") String taskId);

    /**
     * 批量插入基金产品信息
     *
     * @param fundProductInfoList 基金产品信息列表
     * @return 插入的记录数
     */
    int batchInsert(@Param("list") List<FundProductInfo> fundProductInfoList);
}
