package com.icarus.bi.service.biz;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.bi.dto.QueryCriteria;
import com.icarus.bi.dto.BiIdxDataDto;
import com.icarus.bi.entity.*;
import com.icarus.bi.service.*;
import com.icarus.bi.vo.BiIdxDataSetVo;
import com.icarus.bi.vo.ExtraResultVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 指标数据业务处理服务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Slf4j
@Service
public class BiIdxDataBizService {

    @Resource
    private IBiIdxDataService biIdxDataService;

    @Resource
    private IBiIdxConfService biIdxConfService;

    @Resource
    private IBiIdxObjService biIdxObjService;


    public void saveBid(BiIdxData bid){
        LocalDateTime now = LocalDateTime.now();
        bid.setCreateTime(now);
        bid.setUpdateTime(now);
        biIdxDataService.save(bid);
    }

    public void updateBid(BiIdxData bid) {
        BiIdxData byId = biIdxDataService.getById(bid.getId());
        if(null == byId){
            log.error("[指标数据记录更新]失败, 未找到该指标数据记录, req:{}", JSONUtil.toJsonStr(bid));
            throw new RuntimeException("[指标数据记录]更新失败, 未找到该指标数据记录");
        }
        LocalDateTime now = LocalDateTime.now();
        bid.setUpdateTime(now);
        biIdxDataService.updateById(bid);
    }

    public void deleteBid(Integer id) {
        BiIdxData byId = biIdxDataService.getById(id);
        if(null == byId){
            log.error("[指标数据记录删除]失败, 未找到该Id对应的指标数据记录, id:{}", id);
            throw new RuntimeException("[指标数据记录]删除失败, 未找到Id对应的指标数据记录");
        }
        biIdxDataService.removeById(id);
    }

    public List<BiIdxDataDto> search(String keyword) {
        List<BiIdxData> list = null;
        List<BiIdxDataDto> dtos = new ArrayList<BiIdxDataDto>();
        list = biIdxDataService.lambdaQuery()
                .eq(BiIdxData::getIdxConfId, keyword)
                .list();
        if (list != null && list.size() > 0){
            for (BiIdxData bid : list){
                BiIdxDataDto oneDto = new BiIdxDataDto(bid);
                dtos.add(oneDto);
            }
            return dtos;
        }
        list = biIdxDataService.lambdaQuery()
                .eq(BiIdxData::getIdxConfCode, keyword)
                .list();
        if (list != null && list.size() > 0){
            for (BiIdxData bid : list){
                BiIdxDataDto oneDto = new BiIdxDataDto(bid);
                dtos.add(oneDto);
            }
            return dtos;
        }
        list = biIdxDataService.lambdaQuery()
                .eq(BiIdxData::getValue, keyword)
                .list();
        if (list != null && list.size() > 0){
            for (BiIdxData bid : list){
                BiIdxDataDto oneDto = new BiIdxDataDto(bid);
                dtos.add(oneDto);
            }
            return dtos;
        }
        return null;
    }

    public Page<BiIdxDataDto> pageQueryBid(int currentPage, int pageSize, String idxConfId, String idxConfCode, String value) {
        List<BiIdxDataDto> dtos = new ArrayList<BiIdxDataDto>();
        Page<BiIdxData> idxDataPage = biIdxDataService.lambdaQuery()
                .eq(StrUtil.isBlank(idxConfId), BiIdxData::getIdxConfId, idxConfId)
                .eq(StrUtil.isBlank(idxConfCode), BiIdxData::getIdxConfCode, idxConfCode)
                .like(StrUtil.isNotBlank(value), BiIdxData::getValue, value)
                .page(new Page<>(currentPage, pageSize));
        List<BiIdxData> records = idxDataPage.getRecords();
        if (records != null && records.size() > 0){
            for (BiIdxData bid : records){
                BiIdxDataDto oneDto = new BiIdxDataDto(bid);
                dtos.add(oneDto);
            }
        }
        Page<BiIdxDataDto> page = new Page<>(currentPage, pageSize);
        page.setRecords(dtos);
        return page;
    }
    /**
     * 删除任务相关的指标数据
     * 
     * @param taskId 任务ID
     * @return 是否成功
     */
    public boolean deleteIdxDataByTaskId(String taskId) {
        try {
            boolean success = biIdxDataService.deleteByTaskId(taskId);
            log.info("[删除任务指标数据]完成, taskId:{}, 结果:{}", taskId, success);
            return success;
        } catch (Exception e) {
            log.error("[删除任务指标数据]失败, taskId:{}, error:{}", taskId, e.getMessage(), e);
            throw new RuntimeException("删除任务指标数据失败: " + e.getMessage(), e);
        }
    }

//    public List<BiIdxData> queryCriteria(QueryCriteria input) {
////        return biIdxDataService.queryCriteria(input);
//        List<BiIdxData> result = biIdxDataService.queryMock(input);
//        return result;
//    }


    public boolean generateIdxDataFromResults(String objId, String ruleId, List<BiCotResult> extractResults) {
        try {
            log.info("[生成指标数据]开始, objId:{}, ruleId:{}, 抽取结果数量:{}", objId, ruleId, extractResults.size());

            // 1. 查询相关的指标配置
            List<BiIdxConf> idxConfList = biIdxConfService.getActiveByRuleId(ruleId);
            if (idxConfList.isEmpty()) {
                log.warn("[生成指标数据]未找到相关的指标配置, ruleId:{}", ruleId);
                return true; // 没有配置不算错误
            }

            // 2. 将抽取结果转换为Map便于查找
            Map<String, BiCotResult> resultMap = extractResults.stream()
                    .collect(Collectors.toMap(BiCotResult::getElementField, result -> result, (v1, v2) -> v1));

            // 3. 根据指标配置生成指标数据
            List<BiIdxData> idxDataList = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now();

            for (BiIdxConf idxConf : idxConfList) {
                Boolean ifOverwrite = idxConf.getIfOverwrite();
                String elementField = idxConf.getElementField();
                BiCotResult matchResult = resultMap.get(elementField);

                if (ObjectUtil.isNotNull(matchResult)) {
                    // 如果允许覆盖，检查是否需要更新
                    if (Boolean.TRUE.equals(ifOverwrite)) {
                        handleOverwriteData(objId, ruleId, idxConf, matchResult, now);
                    } else {
                        // 不允许覆盖，直接新增
                        BiIdxData idxData = createIdxData(objId, ruleId, idxConf, matchResult, now);
                        if (ObjectUtil.isNotNull(idxData)){
                            idxDataList.add(idxData);
                        }
                    }

                    log.debug("[生成指标数据]匹配成功, 指标:{}, 要素字段:{}, 值:{}",
                            idxConf.getCode(), elementField, matchResult.getElementResult());
                } else {
                    log.warn("[生成指标数据]未找到匹配的抽取结果, 指标:{}, 要素字段:{}",
                            idxConf.getCode(), elementField);
                }
            }

            // 5. 批量保存新增的指标数据
            if (!idxDataList.isEmpty()) {
                boolean success = biIdxDataService.saveBatch(idxDataList);
                log.info("[生成指标数据]保存完成, objId:{}, 生成数量:{}, 结果:{}",
                        objId, idxDataList.size(), success);
                return success;
            } else {
                log.info("[生成指标数据]没有新增数据, objId:{}", objId);
                return true;
            }

        } catch (Exception e) {
            log.error("[生成指标数据]失败, objId:{}, ruleId:{}, error:{}", objId, ruleId, e.getMessage(), e);
            throw new RuntimeException("生成指标数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理覆盖数据
     */
    private void handleOverwriteData(String objId, String ruleId, BiIdxConf idxConf, BiCotResult matchResult, LocalDateTime now) {
        if (StrUtil.isBlank(matchResult.getElementResult())){
            log.warn("[处理覆盖数据]值不存在, 不处理, objId:{}, 索引:{}, 值:{}",
                    objId, idxConf.getCode(), matchResult.getElementResult());
            return;
        }
        // 查询是否存在相同主体的相同指标字段
        BiIdxData queryParam = new BiIdxData();
        queryParam.setObjId(objId);
        queryParam.setIdxConfId(idxConf.getId());
        queryParam.setElementField(idxConf.getElementField());

        // 这里objId + idxConfId + elementField + 主体标识来唯一确定记录
        List<BiIdxData> existingData = biIdxDataService.selectByQurey(queryParam);

        if (CollectionUtil.isNotEmpty(existingData)) {
            // 存在记录，更新
            for (BiIdxData data : existingData){
                data.setValue(matchResult.getElementResult());
                data.setExtractionTime(now);
                data.setUpdateTime(now);
                biIdxDataService.updateById(data);
                log.info("[生成指标数据]更新记录, objId:{}, 指标:{}, 主键:{}, 值:{}",
                        objId, idxConf.getCode(), idxConf.getIfPrimaryKey(), matchResult.getElementResult());
            }

        } else {
            // 不存在记录，新增
            BiIdxData idxData = createIdxData(objId, ruleId, idxConf, matchResult, now);
            if (ObjectUtil.isNotNull(idxData)){
                biIdxDataService.save(idxData);
            }
            log.info("[生成指标数据]新增记录, objId:{}, 指标:{}, 主键:{}, 值:{}",
                    objId, idxConf.getCode(), idxConf.getIfPrimaryKey(), matchResult.getElementResult());
        }
    }

    /**
     * 创建指标数据
     */
    private BiIdxData createIdxData(String objId, String ruleId, BiIdxConf idxConf, BiCotResult matchResult, LocalDateTime now) {
        if (StrUtil.isBlank(matchResult.getElementResult())){
            log.warn("[创建指标数据]值不存在, 不处理, objId:{}, 索引:{}, 值:{}",
                    objId, idxConf.getCode(), matchResult.getElementResult());
            return null;
        }
        BiIdxData idxData = new BiIdxData();
        idxData.setIdxConfId(idxConf.getId());
        idxData.setIdxConfCode(idxConf.getCode());
        idxData.setIdxConfName(idxConf.getName());
        idxData.setIdxConfOrder(idxConf.getOrder() != null ? idxConf.getOrder().toString() : "0");
        idxData.setObjId(objId);
        idxData.setExtractionTime(now);
        idxData.setRuleId(ruleId);
        idxData.setValue(matchResult.getElementResult());
        idxData.setElementField(idxConf.getElementField());
        idxData.setCreateTime(now);
        idxData.setUpdateTime(now);

        log.debug("[生成指标数据]创建记录, objId:{}, 指标:{}, 主键:{}, 值:{}",
                objId, idxConf.getCode(), idxConf.getIfPrimaryKey(), matchResult.getElementResult());
        return idxData;
    }

    /**
     * 获取指标数据展示宽表
     * @return 宽表结果
     */
    public ExtraResultVo getBiIdxDataForWideTable() {

        // 使用LinkedHashMap保持插入顺序
        LinkedHashMap<String, String> headers = new LinkedHashMap<>();

        List<Map<String, Object>> list = new ArrayList<>();
        List<BiIdxObj> objs = biIdxObjService.list();// 获取所有主体对象，多个场景的时候根据场景筛选
        objs.stream().forEach(obj -> {
            log.info("[获取指标数据展示宽表]开始, objId:{}", obj.getId());
            // 按主体对象获取所有指标数据,按照conf顺序返回
            LambdaQueryWrapper<BiIdxData> wrapper = new LambdaQueryWrapper<BiIdxData>()
                    .eq(BiIdxData::getObjId, obj.getId())
                    .orderByAsc(BiIdxData::getIdxConfOrder); // 使用配置顺序排序
            List<BiIdxData> biIdxDataList = biIdxDataService.list(wrapper);
            if (CollectionUtil.isNotEmpty(biIdxDataList)){
                Map<String, Object> map = new HashMap<>();
                biIdxDataList.stream().forEach(data -> {
                    headers.put(data.getIdxConfCode(), data.getIdxConfName());
                    map.put(data.getIdxConfCode(), data.getValue());
                });
                list.add(map);
            }
        });

        //构建返回结果
        ExtraResultVo extraResultVo = new ExtraResultVo();
        extraResultVo.setHeaders(headers);
        extraResultVo.setData(list);
        return extraResultVo;
    }

    public List<BiIdxDataSetVo> queryCriteria(QueryCriteria input) {
        return biIdxDataService.queryCriteria(input);
    }
}
