package com.icarus.bi.service.biz;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.icarus.bi.dto.BiCotTaskCmttPushReq;
import com.icarus.bi.entity.BiCotResult;
import com.icarus.bi.entity.BiIdxConf;
import com.icarus.bi.entity.BiIdxObj;
import com.icarus.bi.service.IBiIdxConfService;
import com.icarus.bi.service.IBiIdxObjService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>
 * 问数-指标对象表 业务服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Slf4j
@Service
public class BiIdxObjBizService {

    @Resource
    private IBiIdxObjService idxObjService;

    @Resource
    private IBiIdxConfService idxConfService;

    public String generateObjFromCmtt(BiCotTaskCmttPushReq req) {
        //确认取那个字段
        BiCotTaskCmttPushReq.DetailDTO detail = Optional.ofNullable(req.getDetail()).orElseThrow(() -> {
            log.error("[generateObjFromCmtt]失败, detail字段不能为空");
            return new RuntimeException("[generateObjFromCmtt]失败, detail字段不能为空");
        });
        String name = Optional.ofNullable(detail.getName())
                .orElseThrow(() -> {
                    log.error("[generateObjFromCmtt]失败, detail.name字段不能为空");
                    return new RuntimeException("[generateObjFromCmtt]失败, detail.name字段不能为空");
                });
        BiIdxObj idxObj = idxObjService.getByName(name);
        if (idxObj != null) {
            log.info("[generateObjFromCmtt]成功, id:{}, name:{}", idxObj.getId(), idxObj.getName());
            return idxObj.getId();
        }
        List<String> tags = detail.getTags();
        idxObj = new BiIdxObj();
        if (!tags.isEmpty()) {
            idxObj.setElementScene(JSONUtil.toJsonStr(tags));
        }
        idxObj.setName(name);
        LocalDateTime now = LocalDateTime.now();
        idxObj.setCreateTime(now);
        idxObj.setUpdateTime(now);
        idxObjService.save(idxObj);
        log.info("[generateObjFromCmtt]成功, id:{}, name:{}", idxObj.getId(), idxObj.getName());
        return idxObj.getId();
    }

    /**
     * 根据抽取结果创建对象
     * @param taskId
     * @param ruleId
     * @param extractResults
     * @return
     */
    public BiIdxObj createObjFromResult(String taskId, String ruleId, List<BiCotResult> extractResults) {

        log.info("[根据抽取结果创建对象]开始, taskId:{}, ruleId:{}, 结果数量:{}",
                taskId, ruleId, Optional.ofNullable(extractResults).map(List::size).orElse(0));

        // 1. 获取rule中的conf配置
        List<BiIdxConf> confList = Optional.ofNullable(ruleId)
                .map(idxConfService::getActiveByRuleId)
                .orElse(Collections.emptyList());

        if (CollectionUtil.isEmpty(confList)) {
            log.warn("[根据抽取结果创建对象]未找到规则配置, ruleId:{}", ruleId);
            return null;
        }

        // 2. 获取conf中的是主键的字段
        Optional<BiIdxConf> primaryKeyConf = confList.stream()
                .filter(conf -> Optional.ofNullable(conf.getIfPrimaryKey()).orElse(false))
                .findFirst();

        if (!primaryKeyConf.isPresent()) {
            log.warn("[根据抽取结果创建对象]未找到主键配置, ruleId:{}", ruleId);
            return null;
        }

        String primaryKeyField = primaryKeyConf.get().getElementField();
        log.debug("[根据抽取结果创建对象]找到主键字段:{}", primaryKeyField);

        // 3. 获取result中的主键字段的值
        Optional<String> primaryKeyValue = Optional.ofNullable(extractResults)
                .orElse(Collections.emptyList())
                .stream()
                .filter(result -> Objects.equals(result.getElementField(), primaryKeyField))
                .map(BiCotResult::getElementResult)
                .filter(Objects::nonNull)
                .filter(value -> !value.trim().isEmpty())
                .findFirst();

        if (!primaryKeyValue.isPresent()) {
            log.warn("[根据抽取结果创建对象]未找到主键值, primaryKeyField:{}", primaryKeyField);
            return null;
        }

        String objName = primaryKeyValue.get();
        log.debug("[根据抽取结果创建对象]主键值:{}", objName);

        // 4. 根据主键字段的值去idx_obj表中查询对象
        BiIdxObj existingObj = idxObjService.lambdaQuery()
                .eq(BiIdxObj::getName, objName)
                .one();

        LocalDateTime now = LocalDateTime.now();

        if (ObjectUtil.isNotNull(existingObj)) {
            // 6. 如果存在，则更新idx_obj对象
            log.info("[根据抽取结果创建对象]对象已存在，更新对象, objId:{}, objName:{}",
                    existingObj.getId(), objName);

            existingObj.setUpdateTime(now);
            existingObj.setTaskId(taskId);

            idxObjService.updateById(existingObj);
            return existingObj;
        } else {
            // 5. 如果不存在，则创建BiIdxObj对象
            log.info("[根据抽取结果创建对象]对象不存在，创建新对象, objName:{}, taskId:{}", objName, taskId);

            BiIdxObj newObj = new BiIdxObj();
            newObj.setName(objName);
            newObj.setIfActive(true);
            newObj.setCreateTime(now);
            newObj.setUpdateTime(now);
            newObj.setTaskId(taskId);
            idxObjService.save(newObj);
            log.info("[根据抽取结果创建对象]新对象创建成功, objId:{}, objName:{}, taskId:{}",
                    newObj.getId(), objName, taskId);

            return newObj;
        }
    }
}
