package com.icarus.bi.service.biz;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.icarus.bi.entity.BiCotRule;
import com.icarus.bi.entity.BiCotTask;
import com.icarus.bi.enums.AuditStatusEnum;
import com.icarus.bi.enums.TaskStatusEnum;
import com.icarus.bi.service.IBiCotRuleService;
import com.icarus.bi.service.IBiCotTaskService;
import com.icarus.common.minio.MinioUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.UUID;

@Slf4j
@Service
public class BiCotTaskBizService {

    @Resource
    private IBiCotTaskService biCotTaskService;

    @Resource
    private IBiCotRuleService biCotRuleService;

    @Resource
    private MinioUtil minioUtil;

    @Value("${minio.agent-bucket-name}")
    private String bucketName;

    /**
     * 新增BiCotTask
     *
     * @param file 上传的文件
     * @param ruleId 规则对象
     * @return 创建的BiCotTask
     */
    public BiCotTask createBiCotTask(MultipartFile file, String ruleId) {
        try {
            log.info("[新增BiCotTask]开始, fileName:{}", file != null ? file.getOriginalFilename() : "null");

            // 1. 参数校验
            if (file == null || file.isEmpty()) {
                log.error("[新增BiCotTask]文件不能为空");
                throw new RuntimeException("文件不能为空");
            }

            BiCotTask biCotTask = new BiCotTask();

            // 2. 设置默认审核状态
            biCotTask.setAuditStatus(AuditStatusEnum.UNDO.getCode());
            log.info("[新增BiCotTask]设置默认审核状态: {}", AuditStatusEnum.UNDO.getCode());

            // 3. 处理规则ID
            if (StrUtil.isBlank(ruleId)) {
                BiCotRule activeRule = getActiveRule();
                if (activeRule == null) {
                    log.error("[新增BiCotTask]未找到启用的规则");
                    throw new RuntimeException("未找到启用的规则，请先启用至少一个抽取规则");
                }

                // 设置规则相关信息
                biCotTask.setRuleId(activeRule.getId());
                biCotTask.setRuleName(activeRule.getName());
                biCotTask.setCanvasId(activeRule.getCanvasId());
                log.info("[新增BiCotTask]使用默认规则: ruleId={}, ruleName={}, canvasId={}",
                        activeRule.getId(), activeRule.getName(), activeRule.getCanvasId());
            } else {
                // 如果指定了ruleId，需要验证并获取规则信息
                BiCotRule rule = biCotRuleService.getRuleById(ruleId);
                if (rule == null) {
                    log.error("[新增BiCotTask]指定的规则不存在: ruleId={}", biCotTask.getRuleId());
                    throw new RuntimeException("指定的规则不存在");
                }

                if (!Boolean.TRUE.equals(rule.getIfActive())) {
                    log.error("[新增BiCotTask]指定的规则未启用: ruleId={}", biCotTask.getRuleId());
                    throw new RuntimeException("指定的规则未启用");
                }

                // 设置规则相关信息
                biCotTask.setRuleId(rule.getId());
                biCotTask.setRuleName(rule.getName());
                biCotTask.setCanvasId(rule.getCanvasId());
                log.info("[新增BiCotTask]使用指定规则: ruleId={}, ruleName={}, canvasId={}",
                        rule.getId(), rule.getName(), rule.getCanvasId());
            }

            // 4. 设置任务状态
            biCotTask.setTaskStatus(TaskStatusEnum.TODO.getCode());

            // 5. 上传文件到Minio
            String fileUrl = uploadFileToMinio(file);
            biCotTask.setSourceFileKey(fileUrl);
            log.info("[新增BiCotTask]文件上传成功: {}", fileUrl);

            // 6. 设置创建时间
            LocalDateTime now = LocalDateTime.now();
            biCotTask.setCreateTime(now);
            biCotTask.setUpdateTime(now);

            // 7. 保存任务
            boolean saveResult = biCotTaskService.saveTask(biCotTask);
            if (!saveResult) {
                log.error("[新增BiCotTask]保存任务失败");
                throw new RuntimeException("保存任务失败");
            }

            log.info("[新增BiCotTask]成功, taskId:{}, ruleId:{}, sourceFileKey:{}",
                    biCotTask.getId(), biCotTask.getRuleId(), biCotTask.getSourceFileKey());

            return biCotTask;

        } catch (Exception e) {
            log.error("[新增BiCotTask]失败, fileName:{}, error:{}",
                    file != null ? file.getOriginalFilename() : "null", e.getMessage(), e);
            throw new RuntimeException("新增BiCotTask失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取启用的规则（唯一一条ifActive=true的规则）
     *
     * @return 启用的规则
     */
    private BiCotRule getActiveRule() {
        try {
            LambdaQueryWrapper<BiCotRule> wrapper = new LambdaQueryWrapper<BiCotRule>()
                    .eq(BiCotRule::getIfActive, true);

            BiCotRule activeRule = biCotRuleService.getOne(wrapper);

            if (activeRule == null) {
                log.warn("[获取启用规则]未找到启用的规则");
                return null;
            }

            log.info("[获取启用规则]成功: ruleId={}, ruleName={}", activeRule.getId(), activeRule.getName());
            return activeRule;

        } catch (Exception e) {
            log.error("[获取启用规则]失败, error:{}", e.getMessage(), e);
            throw new RuntimeException("获取启用规则失败: " + e.getMessage(), e);
        }
    }

    /**
     * 上传文件到Minio
     *
     * @param file 文件
     * @return 文件URL
     */
    private String uploadFileToMinio(MultipartFile file) {
        try {
            // 生成文件名
            String originalFilename = file.getOriginalFilename();

            String objectName = "cot-task/" + UUID.randomUUID() + "/" + originalFilename;

            log.info("[上传文件到Minio]开始, originalFileName:{}, objectName:{}", originalFilename, objectName);

            // 获取文件字节数组
            byte[] fileBytes = file.getBytes();
            String contentType = file.getContentType();

            // 上传文件到Minio (参数顺序: bytes, bucketName, objectName, contentType)
            String fileUrl = minioUtil.uploadFile(fileBytes, bucketName, objectName, contentType);

            log.info("[上传文件到Minio]成功, fileUrl:{}", fileUrl);
            return objectName;

        } catch (Exception e) {
            log.error("[上传文件到Minio]失败, fileName:{}, error:{}", file.getOriginalFilename(), e.getMessage(), e);
            throw new RuntimeException("上传文件失败: " + e.getMessage(), e);
        }
    }
}
