package com.icarus.bi.service.biz;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.bi.dto.BiIdxConfDto;
import com.icarus.bi.entity.BiIdxConf;
import com.icarus.bi.service.IBiIdxConfService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 指标-配置表 业务服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-16
 */
@Slf4j
@Service
public class BiIdxConfBizService {

    @Resource
    private IBiIdxConfService biIdxConfService;

    public void saveBic(BiIdxConf bic) {
        LocalDateTime now = LocalDateTime.now();
        bic.setCreateTime(now);
        bic.setUpdateTime(now);
        biIdxConfService.save(bic);
    }

    public void updateBic(BiIdxConf bic) {
        BiIdxConf byId = biIdxConfService.getById(bic.getId());
        if(null == byId){
            log.error("[指标配置记录更新]失败, 未找到该别名记录, req:{}", JSONUtil.toJsonStr(bic));
            throw new RuntimeException("[指标配置记录]更新失败, 未找到该配置记录");
        }
        LocalDateTime now = LocalDateTime.now();
        bic.setUpdateTime(now);
        biIdxConfService.updateById(bic);
    }

    public void deleteBic(Integer id) {
        BiIdxConf byId = biIdxConfService.getById(id);
        if(null == byId){
            log.error("[指标配置记录删除]失败, 未找到与Id对应的指标配置记录, Id:{}", id);
            throw new RuntimeException("[指标配置记录]删除失败, 未找到对应的配置记录");
        }
        biIdxConfService.removeById(id);
    }

    public List<BiIdxConfDto> search(String keyword) {
        List<BiIdxConf> list = null;
        List<BiIdxConfDto> dtos = new ArrayList<BiIdxConfDto>();
        //按照指标code查询
        BiIdxConf one = biIdxConfService.lambdaQuery()
                .eq(BiIdxConf::getCode, keyword)
                .eq(BiIdxConf::getIfActive,true)
                .one();
        if(null != one){
            BiIdxConfDto dto = new BiIdxConfDto(one);
            dtos.add(dto);
            return dtos;
        }
        //按照指标代码模糊查询
        list = biIdxConfService.lambdaQuery()
                .like(StrUtil.isBlank( keyword),BiIdxConf::getName, keyword)
                .eq(BiIdxConf::getIfActive,true)
                .list();
        if(null != list && list.size() > 0){
            for (BiIdxConf bic : list){
                BiIdxConfDto dto = new BiIdxConfDto(bic);
                dtos.add(dto);
            }
            return dtos;
        }
        //按照对应要素所属场景查询
        list = biIdxConfService.lambdaQuery()
                .eq(BiIdxConf::getElementScene, keyword)
                .eq(BiIdxConf::getIfActive,true)
                .list();
        if(null != list && list.size() > 0){
            for (BiIdxConf bic : list){
                BiIdxConfDto dto = new BiIdxConfDto(bic);
                dtos.add(dto);
            }
            return dtos;
        }
        return null;
    }

    public Page<BiIdxConfDto> pageQueryIdxConf(int currentPage, int pageSize,String code,String name,String elementScene) {
        List<BiIdxConfDto> dtos = new ArrayList<BiIdxConfDto>();
        Page<BiIdxConf> page = biIdxConfService.lambdaQuery()
                .eq(BiIdxConf::getCode, code)
                .like(StrUtil.isNotBlank(name), BiIdxConf::getName, name)
                .eq(BiIdxConf::getElementScene, elementScene)
                .page(new Page<>(currentPage, pageSize));
        List<BiIdxConf> records = page.getRecords();
        if (records != null && records.size() > 0){
            for (BiIdxConf bic : records){
                BiIdxConfDto dto = new BiIdxConfDto(bic);
                dtos.add(dto);
            }
        }
        Page<BiIdxConfDto> pageDto = new Page<>(currentPage, pageSize);
        pageDto.setRecords(dtos);
        return pageDto;
    }

    public void disableAlias(Integer id) {
        BiIdxConf byId = biIdxConfService.getById(id);
        if(null != byId){
            log.error("[停用指标配置]失败, 未找到该指标配置记录, id:{}", id);
            throw new RuntimeException("[停用指标配置]失败, 未找到该指标配置记录");
        }
        byId.setIfActive(false);
        byId.setUpdateTime(LocalDateTime.now());
        biIdxConfService.updateById(byId);
    }

    public void activeAlias(Integer id) {
        BiIdxConf byId = biIdxConfService.getById(id);
        if(null != byId){
            log.error("[启用指标配置]失败, 未找到该指标配置记录, id:{}", id);
            throw new RuntimeException("[启用指标配置]失败, 未找到该指标配置记录");
        }
        byId.setIfActive(true);
        byId.setUpdateTime(LocalDateTime.now());
        biIdxConfService.updateById(byId);
    }

    public List<BiIdxConf> queryByScene(String sceneId) {
        // TODO: 根据具体业务逻辑实现场景筛选
        log.info("根据场景筛选指标配置信息，请求参数：{}", sceneId);
        List<BiIdxConf> list = biIdxConfService.queryByScene(sceneId);
        if(!list.isEmpty()) {
            return list;
        }
        return null;
    }
}
