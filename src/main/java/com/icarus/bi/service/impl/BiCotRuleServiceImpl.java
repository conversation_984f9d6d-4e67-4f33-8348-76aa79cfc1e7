package com.icarus.bi.service.impl;

import com.icarus.bi.entity.BiCotRule;
import com.icarus.bi.dto.BiCotRuleQueryDTO;
import com.icarus.bi.mapper.BiCotRuleMapper;
import com.icarus.bi.service.IBiCotRuleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * <p>
 * 抽取规则配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Slf4j
@Service
public class BiCotRuleServiceImpl extends ServiceImpl<BiCotRuleMapper, BiCotRule> implements IBiCotRuleService {

    @Override
    public boolean saveRule(BiCotRule biCotRule) {
        try {
            LocalDateTime now = LocalDateTime.now();
            // ID由MyBatis-Plus自动生成，无需手动设置
            biCotRule.setCreateTime(now);
            biCotRule.setUpdateTime(now);
            if (biCotRule.getIfActive() == null) {
                biCotRule.setIfActive(false);
            }
            boolean result = this.save(biCotRule);
            log.info("[新增抽取规则]成功, id:{}, code:{}, name:{}", biCotRule.getId(), biCotRule.getCode(), biCotRule.getName());
            return result;
        } catch (Exception e) {
            log.error("[新增抽取规则]失败, code:{}, name:{}, error:{}", biCotRule.getCode(), biCotRule.getName(), e.getMessage());
            throw new RuntimeException("[新增抽取规则]失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean updateRule(BiCotRule biCotRule) {
        try {
            BiCotRule existing = this.getById(biCotRule.getId());
            if (existing == null) {
                log.error("[修改抽取规则]失败, 未找到该记录, id:{}", biCotRule.getId());
                throw new RuntimeException("[修改抽取规则]失败, 未找到该记录");
            }

            biCotRule.setUpdateTime(LocalDateTime.now());
            // 保留原有的创建时间
            biCotRule.setCreateTime(existing.getCreateTime());
            boolean result = this.updateById(biCotRule);
            log.info("[修改抽取规则]成功, id:{}, code:{}, name:{}", biCotRule.getId(), biCotRule.getCode(), biCotRule.getName());
            return result;
        } catch (Exception e) {
            log.error("[修改抽取规则]失败, id:{}, error:{}", biCotRule.getId(), e.getMessage());
            throw new RuntimeException("[修改抽取规则]失败: " + e.getMessage(), e);
        }
    }

    @Override
    public BiCotRule getRuleById(String id) {
        try {
            BiCotRule biCotRule = this.getById(id);
            if (biCotRule == null) {
                log.warn("[获取抽取规则详情]未找到记录, id:{}", id);
            }
            return biCotRule;
        } catch (Exception e) {
            log.error("[获取抽取规则详情]失败, id:{}, error:{}", id, e.getMessage());
            throw new RuntimeException("[获取抽取规则详情]失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Page<BiCotRule> pageQueryRule(BiCotRuleQueryDTO queryDTO) {
        try {
            log.info("[分页查询抽取规则-DTO]开始, queryDTO:{}", queryDTO);

            // 验证参数
            if (queryDTO == null) {
                queryDTO = new BiCotRuleQueryDTO();
            }

            // 验证时间范围
            if (!queryDTO.isValidTimeRange()) {
                throw new IllegalArgumentException("时间范围设置不合理");
            }

            // 创建分页对象
            Page<BiCotRule> page = new Page<>(queryDTO.getCurrentPage(), queryDTO.getPageSize());

            // 构建查询条件
            LambdaQueryWrapper<BiCotRule> queryWrapper = new LambdaQueryWrapper<>();

            // 基础条件
            if (StringUtils.hasText(queryDTO.getCode())) {
                queryWrapper.like(BiCotRule::getCode, queryDTO.getCode());
            }
            if (StringUtils.hasText(queryDTO.getName())) {
                queryWrapper.like(BiCotRule::getName, queryDTO.getName());
            }
            if (StringUtils.hasText(queryDTO.getCanvasId())) {
                queryWrapper.eq(BiCotRule::getCanvasId, queryDTO.getCanvasId());
            }
            if (queryDTO.getIfActive() != null) {
                queryWrapper.eq(BiCotRule::getIfActive, queryDTO.getIfActive());
            }
            if (StringUtils.hasText(queryDTO.getCanvasVersion())) {
                queryWrapper.eq(BiCotRule::getCanvasVersion, queryDTO.getCanvasVersion());
            }

            // 创建时间范围
            if (queryDTO.getCreateTimeStart() != null) {
                queryWrapper.ge(BiCotRule::getCreateTime, queryDTO.getCreateTimeStart());
            }
            if (queryDTO.getCreateTimeEnd() != null) {
                queryWrapper.le(BiCotRule::getCreateTime, queryDTO.getCreateTimeEnd());
            }

            // 更新时间范围
            if (queryDTO.getUpdateTimeStart() != null) {
                queryWrapper.ge(BiCotRule::getUpdateTime, queryDTO.getUpdateTimeStart());
            }
            if (queryDTO.getUpdateTimeEnd() != null) {
                queryWrapper.le(BiCotRule::getUpdateTime, queryDTO.getUpdateTimeEnd());
            }

            // 排序
            String orderBy = queryDTO.getValidOrderBy();
            String orderDirection = queryDTO.getValidOrderDirection();

            if ("asc".equals(orderDirection)) {
                switch (orderBy) {
                    case "createTime":
                        queryWrapper.orderByAsc(BiCotRule::getCreateTime);
                        break;
                    case "updateTime":
                        queryWrapper.orderByAsc(BiCotRule::getUpdateTime);
                        break;
                    case "code":
                        queryWrapper.orderByAsc(BiCotRule::getCode);
                        break;
                    case "name":
                        queryWrapper.orderByAsc(BiCotRule::getName);
                        break;
                    default:
                        queryWrapper.orderByAsc(BiCotRule::getCreateTime);
                }
            } else {
                switch (orderBy) {
                    case "createTime":
                        queryWrapper.orderByDesc(BiCotRule::getCreateTime);
                        break;
                    case "updateTime":
                        queryWrapper.orderByDesc(BiCotRule::getUpdateTime);
                        break;
                    case "code":
                        queryWrapper.orderByDesc(BiCotRule::getCode);
                        break;
                    case "name":
                        queryWrapper.orderByDesc(BiCotRule::getName);
                        break;
                    default:
                        queryWrapper.orderByDesc(BiCotRule::getCreateTime);
                }
            }

            Page<BiCotRule> result = this.page(page, queryWrapper);
            log.info("[分页查询抽取规则-DTO]成功, 当前页:{}, 页大小:{}, 总记录数:{}, 排序:{}_{}",
                    queryDTO.getCurrentPage(), queryDTO.getPageSize(), result.getTotal(), orderBy, orderDirection);
            return result;

        } catch (Exception e) {
            log.error("[分页查询抽取规则-DTO]失败, queryDTO:{}, error:{}", queryDTO, e.getMessage(), e);
            throw new RuntimeException("[分页查询抽取规则-DTO]失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean activeRule(String id) {
        try {
            BiCotRule biCotRule = this.getById(id);
            if (biCotRule == null) {
                log.error("[启用抽取规则]失败, 未找到该记录, id:{}", id);
                throw new RuntimeException("[启用抽取规则]失败, 未找到该记录");
            }
            biCotRule.setIfActive(true);
            biCotRule.setUpdateTime(LocalDateTime.now());
            boolean result = this.updateById(biCotRule);
            log.info("[启用抽取规则]成功, id:{}, code:{}", id, biCotRule.getCode());
            return result;
        } catch (Exception e) {
            log.error("[启用抽取规则]失败, id:{}, error:{}", id, e.getMessage());
            throw new RuntimeException("[启用抽取规则]失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean disableRule(String id) {
        try {
            BiCotRule biCotRule = this.getById(id);
            if (biCotRule == null) {
                log.error("[禁用抽取规则]失败, 未找到该记录, id:{}", id);
                throw new RuntimeException("[禁用抽取规则]失败, 未找到该记录");
            }
            biCotRule.setIfActive(false);
            biCotRule.setUpdateTime(LocalDateTime.now());
            boolean result = this.updateById(biCotRule);
            log.info("[禁用抽取规则]成功, id:{}, code:{}", id, biCotRule.getCode());
            return result;
        } catch (Exception e) {
            log.error("[禁用抽取规则]失败, id:{}, error:{}", id, e.getMessage());
            throw new RuntimeException("[禁用抽取规则]失败: " + e.getMessage(), e);
        }
    }
    @Override
    public BiCotRule getRuleByCode(String code) {
        try {
            LambdaQueryWrapper<BiCotRule> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BiCotRule::getCode, code);
            // 如果存在多条相同code的记录，获取最新创建的
            queryWrapper.orderByDesc(BiCotRule::getCreateTime);
            queryWrapper.last("LIMIT 1");

            BiCotRule biCotRule = this.getOne(queryWrapper, false); // false表示允许多条记录时不抛异常
            if (biCotRule == null) {
                log.warn("[根据规则Code查询抽取规则]未找到记录, code:{}", code);
            } else {
                log.info("[根据规则Code查询抽取规则]成功, code:{}, ruleId:{}", code, biCotRule.getId());
            }
            return biCotRule;
        } catch (Exception e) {
            log.error("[根据规则Code查询抽取规则]失败, code:{}, error:{}", code, e.getMessage());
            throw new RuntimeException("[根据规则Code查询抽取规则]失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Page<BiCotRule> getRulesByCanvasId(String canvasId, int currentPage, int pageSize) {
        try {
            Page<BiCotRule> page = new Page<>(currentPage, pageSize);
            LambdaQueryWrapper<BiCotRule> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BiCotRule::getCanvasId, canvasId);
            queryWrapper.orderByDesc(BiCotRule::getCreateTime);

            Page<BiCotRule> result = this.page(page, queryWrapper);
            log.info("[根据画布ID查询规则列表]成功, canvasId:{}, 总记录数:{}", canvasId, result.getTotal());
            return result;
        } catch (Exception e) {
            log.error("[根据画布ID查询规则列表]失败, canvasId:{}, error:{}", canvasId, e.getMessage());
            throw new RuntimeException("[根据画布ID查询规则列表]失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean deleteRule(String id) {
        try {
            BiCotRule biCotRule = this.getById(id);
            if (biCotRule == null) {
                log.error("[删除抽取规则]失败, 未找到该记录, id:{}", id);
                throw new RuntimeException("[删除抽取规则]失败, 未找到该记录");
            }
            boolean result = this.removeById(id);
            log.info("[删除抽取规则]成功, id:{}, code:{}", id, biCotRule.getCode());
            return result;
        } catch (Exception e) {
            log.error("[删除抽取规则]失败, id:{}, error:{}", id, e.getMessage());
            throw new RuntimeException("[删除抽取规则]失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Page<BiCotRule> getRulesByActiveStatus(Boolean ifActive, int currentPage, int pageSize) {
        try {
            Page<BiCotRule> page = new Page<>(currentPage, pageSize);
            LambdaQueryWrapper<BiCotRule> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BiCotRule::getIfActive, ifActive);
            queryWrapper.orderByDesc(BiCotRule::getCreateTime);

            Page<BiCotRule> result = this.page(page, queryWrapper);
            log.info("[根据启用状态查询规则列表]成功, ifActive:{}, 总记录数:{}", ifActive, result.getTotal());
            return result;
        } catch (Exception e) {
            log.error("[根据启用状态查询规则列表]失败, ifActive:{}, error:{}", ifActive, e.getMessage());
            throw new RuntimeException("[根据启用状态查询规则列表]失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean updateCanvasVersion(String id, String canvasVersion) {
        try {
            BiCotRule biCotRule = this.getById(id);
            if (biCotRule == null) {
                log.error("[更新画布版本号]失败, 未找到该记录, id:{}", id);
                throw new RuntimeException("[更新画布版本号]失败, 未找到该记录");
            }
            biCotRule.setCanvasVersion(canvasVersion);
            biCotRule.setUpdateTime(LocalDateTime.now());
            boolean result = this.updateById(biCotRule);
            log.info("[更新画布版本号]成功, id:{}, canvasVersion:{}", id, canvasVersion);
            return result;
        } catch (Exception e) {
            log.error("[更新画布版本号]失败, id:{}, canvasVersion:{}, error:{}", id, canvasVersion, e.getMessage());
            throw new RuntimeException("[更新画布版本号]失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Page<BiCotRule> getRulesByCanvasVersion(String canvasVersion, int currentPage, int pageSize) {
        try {
            Page<BiCotRule> page = new Page<>(currentPage, pageSize);
            LambdaQueryWrapper<BiCotRule> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BiCotRule::getCanvasVersion, canvasVersion);
            queryWrapper.orderByDesc(BiCotRule::getCreateTime);

            Page<BiCotRule> result = this.page(page, queryWrapper);
            log.info("[根据画布版本号查询规则列表]成功, canvasVersion:{}, 总记录数:{}", canvasVersion, result.getTotal());
            return result;
        } catch (Exception e) {
            log.error("[根据画布版本号查询规则列表]失败, canvasVersion:{}, error:{}", canvasVersion, e.getMessage());
            throw new RuntimeException("[根据画布版本号查询规则列表]失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取启用的规则
     * @return
     */
    @Override
    public BiCotRule getActiveRule() {
        try {
            LambdaQueryWrapper<BiCotRule> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BiCotRule::getIfActive, true);
            // 按创建时间倒序排列，获取最新创建的启用规则
            queryWrapper.orderByDesc(BiCotRule::getCreateTime);
            queryWrapper.last("LIMIT 1"); // 限制只返回1条记录

            BiCotRule activeRule = this.getOne(queryWrapper, false); // false表示允许多条记录时不抛异常

            if (activeRule == null) {
                log.warn("[获取启用规则]未找到启用的抽取规则");
            } else {
                log.info("[获取启用规则]成功, ruleId:{}, ruleName:{}", activeRule.getId(), activeRule.getName());
            }

            return activeRule;
        } catch (Exception e) {
            log.error("[获取启用规则]失败, error:{}", e.getMessage(), e);
            throw new RuntimeException("获取启用规则失败: " + e.getMessage(), e);
        }
    }
}
