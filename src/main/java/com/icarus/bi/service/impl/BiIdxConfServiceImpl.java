package com.icarus.bi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.bi.entity.BiIdxConf;
import com.icarus.bi.mapper.BiIdxConfMapper;
import com.icarus.bi.service.IBiIdxConfService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

import java.util.List;

/**
 * <p>
 * 指标开发表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@Slf4j
@Service
public class BiIdxConfServiceImpl extends ServiceImpl<BiIdxConfMapper, BiIdxConf> implements IBiIdxConfService {

    public List<BiIdxConf> getActiveByRuleId(String ruleId) {
        try {
            LambdaQueryWrapper<BiIdxConf> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BiIdxConf::getRuleId, ruleId);
            queryWrapper.eq(BiIdxConf::getIfActive, true);
            queryWrapper.orderByAsc(BiIdxConf::getOrder);

            List<BiIdxConf> result = this.list(queryWrapper);
            log.info("[根据ruleId查询启用指标配置]成功, ruleId:{}, 配置数量:{}", ruleId, result.size());
            return result;

        } catch (Exception e) {
            log.error("[根据ruleId查询启用指标配置]失败, ruleId:{}, error:{}", ruleId, e.getMessage(), e);
            throw new RuntimeException("查询指标配置失败: " + e.getMessage(), e);
        }
    }
    @Override
    public List<BiIdxConf> queryByScene(String sceneId) {
        // TODO: 根据具体业务逻辑实现场景筛选
        log.info("根据场景筛选指标配置信息，请求参数：{}", sceneId);
        if (StringUtils.hasText(sceneId)) {
            List<BiIdxConf> list = lambdaQuery()
                    .eq(BiIdxConf::getElementScene, sceneId)
                    .eq(BiIdxConf::getIfActive, true)
                    .orderByAsc(BiIdxConf::getOrder)
                    .list();
            if(!list.isEmpty()) {
                return list;
            }
        }else {
            List<BiIdxConf> list = lambdaQuery()
                    .eq(BiIdxConf::getIfActive, true)
                    .orderByAsc(BiIdxConf::getOrder)
                    .list();
            return list;
        }
        return null;
    }

    @Override
    public boolean saveIdxConf(BiIdxConf biIdxConf) {
        log.info("新增指标配置，参数：{}", biIdxConf);
        biIdxConf.setCreateTime(LocalDateTime.now());
        biIdxConf.setUpdateTime(LocalDateTime.now());
        return save(biIdxConf);
    }

    @Override
    public boolean updateIdxConf(BiIdxConf biIdxConf) {
        log.info("修改指标配置，参数：{}", biIdxConf);
        biIdxConf.setUpdateTime(LocalDateTime.now());
        return updateById(biIdxConf);
    }

    @Override
    public boolean deleteIdxConfById(String id) {
        log.info("删除指标配置，ID：{}", id);
        return removeById(id);
    }

    @Override
    public BiIdxConf getIdxConfById(String id) {
        log.info("根据ID查询指标配置，ID：{}", id);
        return getById(id);
    }

    @Override
    public Page<BiIdxConf> pageQueryIdxConf(int currentPage, int pageSize, String code, String name) {
        log.info("分页查询指标配置，当前页：{}，页大小：{}，code：{}，name：{}", currentPage, pageSize, code, name);

        Page<BiIdxConf> page = new Page<>(currentPage, pageSize);
        LambdaQueryWrapper<BiIdxConf> queryWrapper = new LambdaQueryWrapper<>();

        // 根据code模糊查询
        if (StringUtils.hasText(code)) {
            queryWrapper.like(BiIdxConf::getCode, code);
        }

        // 根据name模糊查询
        if (StringUtils.hasText(name)) {
            queryWrapper.like(BiIdxConf::getName, name);
        }

        // 按创建时间倒序排列
        queryWrapper.orderByDesc(BiIdxConf::getCreateTime);

        return page(page, queryWrapper);
    }
}
