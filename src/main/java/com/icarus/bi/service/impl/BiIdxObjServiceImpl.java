package com.icarus.bi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.icarus.bi.entity.BiAlias;
import com.icarus.bi.entity.BiIdxObj;
import com.icarus.bi.mapper.BiIdxObjMapper;
import com.icarus.bi.service.IBiIdxObjService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.bi.service.biz.BiAliasBizService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 数据源配置表（预留暂时不用） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-16
 */
@Slf4j
@Service
public class BiIdxObjServiceImpl extends ServiceImpl<BiIdxObjMapper, BiIdxObj> implements IBiIdxObjService {

    @Resource
    BiAliasBizService biAliasBizService;
    @Override
    public BiIdxObj getByName(String sourceDataName) {
        return lambdaQuery()
                .eq(BiIdxObj::getName, sourceDataName)
                .one();
    }

    @Override
    public List<BiIdxObj> getListByName(String productName) {
        List<BiIdxObj> biIdxObj;
        if(StringUtils.hasText(productName)) {
            BiAlias biAlias = biAliasBizService.search(productName);
            if(Objects.nonNull(biAlias)){
                productName = biAlias.getFullName();
            }
        }
        if(StringUtils.hasText(productName)) {
            biIdxObj = lambdaQuery().eq(BiIdxObj::getName, productName)
                    .eq(BiIdxObj::getIfActive, true)
                    .list();
        }else {
            biIdxObj = lambdaQuery().eq(BiIdxObj::getIfActive, true)
                    .list();
        }
        if(biIdxObj.isEmpty()){
            //todo 先写成查全部的
            biIdxObj = lambdaQuery().eq(BiIdxObj::getIfActive, true)
                    .list();
        }
        return biIdxObj;
    }

    /**
     * 搜索
     * @param keyword
     * @param limit
     * @return
     */
    public List<BiIdxObj> query(String keyword, Integer limit) {
        LambdaQueryWrapper<BiIdxObj> queryWrapper = new LambdaQueryWrapper<BiIdxObj>()
                .like(BiIdxObj::getName, keyword)
                .orderByDesc(BiIdxObj::getCreateTime);

        if (limit != null && limit > 0) {
            queryWrapper.last("limit " + limit);
        }

        return list(queryWrapper);
    }
}
