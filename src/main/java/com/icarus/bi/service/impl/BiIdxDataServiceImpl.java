package com.icarus.bi.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.bi.dto.QueryCondition;
import com.icarus.bi.dto.QueryCriteria;
import com.icarus.bi.entity.*;
import com.icarus.bi.mapper.BiIdxDataMapper;
import com.icarus.bi.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.bi.service.IBiIdxDataService;
import com.icarus.bi.vo.BiIdxDataSetVo;
import com.icarus.bi.vo.BiIdxDataVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.Arrays;
import java.util.function.Function;
import java.util.stream.Collectors;

import java.util.List;

/**
 * 指标数据扩展表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@Slf4j
@Service
public class BiIdxDataServiceImpl extends ServiceImpl<BiIdxDataMapper, BiIdxData> implements IBiIdxDataService {

    @Resource
    private IBiCotTaskService biCotTaskService;

    @Resource
    private IBiIdxConfService biIdxConfService;

    @Resource
    private IBiIdxObjService biObjService;

    @Override
    public boolean deleteByTaskId(String taskId) {
        try {
            QueryWrapper<BiIdxData> queryWrapper = new QueryWrapper<>();
//            queryWrapper.eq("task_id", taskId);

            boolean success = this.remove(queryWrapper);
            log.info("[根据任务ID删除指标数据]完成, taskId:{}, 结果:{}", taskId, success);
            return success;

        } catch (Exception e) {
            log.error("[根据任务ID删除指标数据]失败, taskId:{}, error:{}", taskId, e.getMessage(), e);
            throw new RuntimeException("删除指标数据失败: " + e.getMessage(), e);
        }
    }
    @Override
    public List<BiIdxDataSetVo> queryCriteria(QueryCriteria queryCriteria) {
        try {
            List<BiIdxDataSetVo> result = new ArrayList<>();
            log.info("开始执行动态查询，查询条件: {}", JSONUtil.toJsonStr(queryCriteria));
            //查询conf表
            List<BiIdxConf> confs = biIdxConfService.list();
            Set<String> confCodeSet = confs.stream()
                    .filter(conf -> Boolean.TRUE.equals(conf.getIfActive()))    // 筛选 ifActive 为 true
                    .map(BiIdxConf::getCode)           // 取出 code
                    .collect(Collectors.toSet());
            //查询bi_idx_obj表
            List<BiIdxObj> biIdxObj = biObjService.getListByName((Objects.nonNull(queryCriteria.getProductName())&&!queryCriteria.getProductName().isEmpty())?queryCriteria.getProductName().get(0): "");
            Map<String,String> objId2Name = biIdxObj.stream()
                    .filter(obj -> obj.getId() != null && obj.getName() != null) // 过滤掉id或name为null的对象
                    .collect(Collectors.toMap(BiIdxObj::getId, BiIdxObj::getName));
            Map<String,String> objId2TaskIdMap = biIdxObj.stream()
                    .filter(obj -> obj.getId() != null && obj.getTaskId() != null) // 过滤掉id或taskId为null的对象
                    .collect(Collectors.toMap(BiIdxObj::getId, BiIdxObj::getTaskId));            //根据objId查询bi_idx_data表，同时应用QueryCondition过滤条件
            List<String> objIds = biIdxObj.stream()
                .map(BiIdxObj::getId)
                .collect(Collectors.toList());
            
            // 检查objIds是否为空，避免生成空的IN查询
            if (objIds.isEmpty()) {
                log.warn("未找到符合条件的objId，返回空结果");
                return new ArrayList<>();
            }
            
            LambdaQueryWrapper<BiIdxData> queryWrapper = new QueryWrapper<BiIdxData>().lambda();
            queryWrapper.in(BiIdxData::getObjId, objIds);
            //BiIdxData.getValue 不为空
            queryWrapper.isNotNull(BiIdxData::getValue);
            queryWrapper.ne(BiIdxData::getValue, "");
            //获取所有指标数据
            List<BiIdxData> dataList = this.list(queryWrapper);
            log.info("查询到{}条数据", dataList.size());
             
             // 转换为BiIdxDataVo
             List<BiIdxDataVo> biIdxDataVoList = dataList.stream()
                 .map(BiIdxDataVo::convert)
                 .collect(Collectors.toList());
             
             log.info("转换为BiIdxDataVo完成，数量: {}", biIdxDataVoList.size());
             
             // 根据objId进行分组
             Map<String, List<BiIdxDataVo>> groupedByObjId = biIdxDataVoList.stream()
                 .filter(vo -> vo.getProductId() != null && !vo.getProductId().trim().isEmpty())
                 .collect(Collectors.groupingBy(BiIdxDataVo::getProductId));
             
             log.info("按objId分组完成，分组数量: {}", groupedByObjId.size());
             
             // 转换成BiIdxDataSetVo，并对每个分组应用条件过滤
             for (Map.Entry<String, List<BiIdxDataVo>> entry : groupedByObjId.entrySet()) {
                 List<BiIdxDataVo> entryList = entry.getValue();



                 // 检查数据列表是否为空
                 if (entryList == null || entryList.isEmpty()) {
                     log.warn("分组数据为空，跳过该分组: {}", entry.getKey());
                     continue;
                 }
                 
                 // 获取产品名称，增加空指针检查
                 String productName = null;
                 BiIdxDataVo firstData = entryList.get(0);
                 if (firstData != null && firstData.getProductId() != null) {
                     productName = objId2Name.get(firstData.getProductId());
                 }
                 
                 // 如果产品名称为空，则不加入result中
                 if (productName == null || productName.trim().isEmpty()) {
                     log.warn("产品名称为空，跳过该分组: {}", entry.getKey());
                     continue;
                 }
                 
                 // 检查entryList是否符合预设的conditions
                 boolean shouldInclude = true;
                 log.info("检查分组 {} 的查询条件，queryCriteria.getConditions()是否为null: {}", 
                     entry.getKey(), queryCriteria.getConditions() == null);
                 if (queryCriteria.getConditions() != null) {
                     log.info("分组 {} 的查询条件数量: {}", entry.getKey(), queryCriteria.getConditions().size());
                 }
                 
                 if (queryCriteria.getConditions() != null && !queryCriteria.getConditions().isEmpty()) {
                     log.info("对分组 {} 应用查询条件过滤，条件数量: {}", entry.getKey(), queryCriteria.getConditions().size());
                     
                     // 检查分组是否满足所有条件
                     shouldInclude = checkGroupMeetsConditions(entryList, queryCriteria.getConditions(), confCodeSet);
                     
                     if (shouldInclude) {
                         log.info("分组 {} 符合查询条件", entry.getKey());
                     } else {
                         log.info("分组 {} 不符合查询条件，跳过", entry.getKey());
                     }
                 }
                 
                 // 仅当entryList符合预设的conditions时，才将dataSetVo添加到result列表中
                 if (shouldInclude) {
                     // 根据objId查询对应的taskId
                     String objId = entry.getKey();
                     String taskId = objId2TaskIdMap.get(objId);

                     if (taskId != null && !taskId.trim().isEmpty()) {
                         log.info("为分组 {} 查询task信息，taskId: {}", objId, taskId);

                         try {
                             // 查询bi_cot_task表获取source_file_key
                             BiCotTask cotTask = biCotTaskService.getTaskById(taskId);
                             if (cotTask != null && cotTask.getSourceFileKey() != null && !cotTask.getSourceFileKey().trim().isEmpty()) {
                                 log.info("查询到task信息，sourceFileKey: {}", cotTask.getSourceFileKey());

                                 // 创建新的BiIdxDataVo对象
                                 BiIdxDataVo newDataVo = new BiIdxDataVo();
                                 newDataVo.setIdxConfName("taskId");
                                 newDataVo.setIdxConfCode(productName);
                                 newDataVo.setValue(taskId);
                                 newDataVo.setProductId(objId);
                                 newDataVo.setProductName(productName);

                                 // 添加到entryList中
                                 entryList.add(newDataVo);
                                 log.info("已为分组 {} 添加新的BiIdxDataVo对象，idxConfName: {}, value: {}", objId, cotTask.getSourceFileKey(), taskId);
                             } else {
                                 log.warn("未找到task信息或sourceFileKey为空，taskId: {}", taskId);
                             }
                         } catch (Exception e) {
                             log.error("查询task信息失败，taskId: {}, error: {}", taskId, e.getMessage(), e);
                         }
                     } else {
                         log.warn("分组 {} 对应的taskId为空，跳过添加新的BiIdxDataVo", objId);
                     }

                     // 根据QueryCriteria的targets字段对entryList进行筛选
                     List<BiIdxDataVo> filteredEntryList = filterByTargets(entryList, queryCriteria.getTargets(),queryCriteria.getConditions());
                     
                     BiIdxDataSetVo dataSetVo = new BiIdxDataSetVo();
                     dataSetVo.setProductName(productName);
                     dataSetVo.setData(filteredEntryList);
                     result.add(dataSetVo);
                     log.info("已添加分组 {} 到结果集，产品名称: {}，筛选前数据量: {}，筛选后数据量: {}", entry.getKey(), productName, entryList.size(), filteredEntryList.size());
                 }
             }
             
             log.info("转换为BiIdxDataSetVo完成，结果数量: {}", result.size());



            log.info("动态查询完成，总共返回记录数: {}", result.size());
            //idxDataResults转换成BiIdxDataVo
            return result;
            
        } catch (Exception e) {
            log.error("动态查询执行失败", e);
            throw new RuntimeException("查询失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean saveIdxData(BiIdxData biIdxData) {
        log.info("新增指标数据，参数：{}", biIdxData);
        biIdxData.setCreateTime(LocalDateTime.now());
        biIdxData.setUpdateTime(LocalDateTime.now());
        return save(biIdxData);
    }

    @Override
    public boolean updateIdxData(BiIdxData biIdxData) {
        log.info("修改指标数据，参数：{}", biIdxData);
        biIdxData.setUpdateTime(LocalDateTime.now());
        return updateById(biIdxData);
    }

    @Override
    public boolean deleteIdxDataById(String id) {
        log.info("删除指标数据，ID：{}", id);
        return removeById(id);
    }

    @Override
    public BiIdxData getIdxDataById(String id) {
        log.info("根据ID查询指标数据，ID：{}", id);
        return getById(id);
    }

    @Override
    public Page<BiIdxData> pageQueryIdxData(int currentPage, int pageSize, String idxConfId, String minioKey) {
        log.info("分页查询指标数据，当前页：{}，页大小：{}，指标配置ID：{}，文件Key：{}", currentPage, pageSize, idxConfId, minioKey);

        Page<BiIdxData> page = new Page<>(currentPage, pageSize);
        QueryWrapper<BiIdxData> queryWrapper = new QueryWrapper<>();

        // 根据指标配置ID查询
        if (StringUtils.hasText(idxConfId)) {
            queryWrapper.eq("idx_conf_id", idxConfId);
        }

        // 根据文件Key模糊查询
        if (StringUtils.hasText(minioKey)) {
            queryWrapper.like("minio_key", minioKey);
        }

        // 按创建时间倒序排列
        queryWrapper.orderByDesc("create_time");

        return page(page, queryWrapper);
    }

    /**
     *
     * @param cotResults
     * @param taskId
     */
    @Override
    public void saveBatch(List<BiCotResult> cotResults, String taskId) {
        BiCotTask taskById = biCotTaskService.getTaskById(taskId);
        String ruleId = taskById.getRuleId();

        List<BiIdxConf> activeByRuleId = biIdxConfService.getActiveByRuleId(ruleId);

        //根据conf转换BiCotResult为BiIdxData
        List<BiIdxData> biIdxData = this.baseMapper.selectList(new LambdaQueryWrapper<BiIdxData>().eq(BiIdxData::getRuleId, ruleId));
    }

    @Override
    public List<BiIdxData> queryMock(QueryCriteria input) {
        return lambdaQuery().eq(BiIdxData::getObjId, "obj_1_fee").list();
    }

    /**
     * 根据查询条件查询单个
     * @param query 查询条件
     * @return List<BiIdxData>
     */
    @Override
    public List<BiIdxData> selectByQurey(BiIdxData query){
        if (Objects.isNull(query)){
            return this.list();
        }
        LambdaQueryWrapper<BiIdxData> queryWrapper = new LambdaQueryWrapper<>();

        // ID精确查询
        if (StrUtil.isNotBlank(query.getId())) {
            queryWrapper.eq(BiIdxData::getId, query.getId());
        }

        // 指标配置ID精确查询
        if (StrUtil.isNotBlank(query.getIdxConfId())) {
            queryWrapper.eq(BiIdxData::getIdxConfId, query.getIdxConfId());
        }

        // 指标code模糊查询
        if (StrUtil.isNotBlank(query.getIdxConfCode())) {
            queryWrapper.like(BiIdxData::getIdxConfCode, query.getIdxConfCode());
        }

        // 指标配置顺序精确查询
        if (StrUtil.isNotBlank(query.getIdxConfOrder())) {
            queryWrapper.eq(BiIdxData::getIdxConfOrder, query.getIdxConfOrder());
        }

        // 描述对象ID精确查询
        if (StrUtil.isNotBlank(query.getObjId())) {
            queryWrapper.eq(BiIdxData::getObjId, query.getObjId());
        }

        // 描述对象名称模糊查询
        if (StrUtil.isNotBlank(query.getObjName())) {
            queryWrapper.like(BiIdxData::getObjName, query.getObjName());
        }

        // 抽取规则ID精确查询
        if (StrUtil.isNotBlank(query.getRuleId())) {
            queryWrapper.eq(BiIdxData::getRuleId, query.getRuleId());
        }

        // 指标值模糊查询
        if (StrUtil.isNotBlank(query.getValue())) {
            queryWrapper.like(BiIdxData::getValue, query.getValue());
        }

        // 对应要素字段code精确查询
        if (StrUtil.isNotBlank(query.getElementField())) {
            queryWrapper.eq(BiIdxData::getElementField, query.getElementField());
        }

        // 抽取时间范围查询（如果有值，则查询该时间之后的记录）
        if (Objects.nonNull(query.getExtractionTime())) {
            queryWrapper.ge(BiIdxData::getExtractionTime, query.getExtractionTime());
        }

        // 创建时间范围查询（如果有值，则查询该时间之后的记录）
        if (Objects.nonNull(query.getCreateTime())) {
            queryWrapper.ge(BiIdxData::getCreateTime, query.getCreateTime());
        }

        // 更新时间范围查询（如果有值，则查询该时间之后的记录）
        if (Objects.nonNull(query.getUpdateTime())) {
            queryWrapper.ge(BiIdxData::getUpdateTime, query.getUpdateTime());
        }

        // 按创建时间倒序排列
        queryWrapper.orderByDesc(BiIdxData::getCreateTime);

        return this.list(queryWrapper);
    }

    /**
     * 检查分组是否满足所有查询条件
     * 
     * @param groupData 分组数据列表
     * @param conditions 查询条件列表
     * @param confCodeSet 有效的配置代码集合
     * @return 是否满足所有条件
     */
    private boolean checkGroupMeetsConditions(List<BiIdxDataVo> groupData, List<QueryCondition> conditions, Set<String> confCodeSet) {
        try {
            // 首先，对 conditions 列表进行去重过滤，按 field 字段去重，保留第一个出现的条件
            List<QueryCondition> deduplicatedConditions = conditions.stream()
                .collect(Collectors.toMap(
                    QueryCondition::getField,  // 按 field 字段作为 key
                    Function.identity(),        // value 为条件对象本身
                    (existing, replacement) -> existing,  // 保留第一个出现的条件
                    LinkedHashMap::new          // 保持原有顺序
                ))
                .values()
                .stream()
                .collect(Collectors.toList());
            
            log.info("条件去重完成，原始条件数量: {}, 去重后条件数量: {}", conditions.size(), deduplicatedConditions.size());
            
            log.debug("检查分组是否满足条件，分组数据量: {}, 条件数量: {}", groupData.size(), deduplicatedConditions.size());
            
            // 如果没有条件，返回true
            if (deduplicatedConditions.isEmpty()) {
                return true;
            }
            
            // 过滤有效的查询条件
            List<QueryCondition> validConditions = deduplicatedConditions.stream()
                .filter(condition -> condition.getField() != null && confCodeSet.contains(condition.getField()))
                .collect(Collectors.toList());
            
            if (validConditions.isEmpty()) {
                log.debug("没有有效的查询条件，返回true");
                return true;
            }
            
            log.debug("有效条件数量: {}", validConditions.size());
            
            // 检查该分组是否满足所有条件
             boolean allConditionsMet = validConditions.stream().allMatch(condition -> {
                 log.info("检查条件: 字段={}, 操作符={}, 期望值={}", condition.getField(), condition.getOperator(), condition.getValue());
                 
                 // 查找该分组中匹配字段的数据
                 List<BiIdxDataVo> fieldData = groupData.stream()
                     .filter(data -> condition.getField().equals(data.getIdxConfCode()))
                     .collect(Collectors.toList());
                 
                 log.info("找到匹配字段 {} 的数据条数: {}", condition.getField(), fieldData.size());
                 
                 if (fieldData.isEmpty()) {
                     log.info("字段 {} 在分组中未找到数据，条件不满足", condition.getField());
                     return false;
                 }
                 
                 // 检查是否有满足条件的值
                  boolean conditionMet = fieldData.stream().anyMatch(data -> {
                      boolean matches = matchesCondition(data.getValue(), condition.getOperator(), condition.getValue());
                      log.info("字段 {} 实际值 {} {} 期望值 {} = {}", 
                          condition.getField(), data.getValue(), condition.getOperator(), 
                          condition.getValue(), matches);
                      return matches;
                  });
                 
                 log.info("条件 {} {} {} 结果: {}", condition.getField(), condition.getOperator(), condition.getValue(), conditionMet);
                 return conditionMet;
             });
             
             log.info("分组条件检查完成，所有条件满足: {}", allConditionsMet);
             return allConditionsMet;
            
        } catch (Exception e) {
            log.error("检查分组条件失败", e);
            return false;
        }
    }
    
    /**
     * 检查值是否满足条件
     * 
     * @param actualValue 实际值
     * @param operator 操作符
     * @param expectedValue 期望值
     * @return 是否满足条件
     */
    private boolean matchesCondition(String actualValue, String operator, String expectedValue) {
        if (actualValue == null) {
            actualValue = "";
        }
        if (expectedValue == null) {
            expectedValue = "";
        }
        //如果是%忽略
        if(expectedValue.equals("%")){
            return false;
        }
        
        switch (operator.toLowerCase()) {
            case "eq":
            case "equals":
            case "=":
                return actualValue.equals(expectedValue);
                
            case "ne":
            case "not_equals":
            case "!=":
                return !actualValue.equals(expectedValue);
                
            case "like":
            case "contains":
                // 支持SQL风格的%通配符
                if (expectedValue.contains("%")) {
                    // 处理SQL风格的LIKE模式
                    String pattern = expectedValue.replace("%", ".*");
                    return actualValue.matches(pattern);
                } else {
                    // 简单的包含匹配
                    return actualValue.contains(expectedValue);
                }
                
            case "not_like":
            case "not_contains":
                // 支持SQL风格的%通配符
                if (expectedValue.contains("%")) {
                    // 处理SQL风格的NOT LIKE模式
                    String pattern = expectedValue.replace("%", ".*");
                    return !actualValue.matches(pattern);
                } else {
                    // 简单的不包含匹配
                    return !actualValue.contains(expectedValue);
                }
            case "gt":
            case ">":
                try {
                    return parseAndCompare(actualValue, expectedValue) > 0;
                } catch (NumberFormatException e) {
                    return actualValue.compareTo(expectedValue) > 0;
                }

            case "ge":
            case ">=":
                try {
                    return parseAndCompare(actualValue, expectedValue) >= 0;
                } catch (NumberFormatException e) {
                    return actualValue.compareTo(expectedValue) >= 0;
                }

            case "lt":
            case "<":
                try {
                    return parseAndCompare(actualValue, expectedValue) < 0;
                } catch (NumberFormatException e) {
                    return actualValue.compareTo(expectedValue) < 0;
                }

            case "le":
            case "<=":
                try {
                    return parseAndCompare(actualValue, expectedValue) <= 0;
                } catch (NumberFormatException e) {
                    return actualValue.compareTo(expectedValue) <= 0;
                }

            case "in":
                List<String> inValues = Arrays.asList(expectedValue.split(","));
                return inValues.contains(actualValue);
                
            case "not_in":
                List<String> notInValues = Arrays.asList(expectedValue.split(","));
                return !notInValues.contains(actualValue);
                
            default:
                log.warn("不支持的操作符: {}, 使用默认等值比较", operator);
                return actualValue.equals(expectedValue);
        }
    }

    /**
     * 解析并比较两个值，支持百分比格式
     * @param actualValue 实际值
     * @param expectedValue 期望值
     * @return 比较结果：>0 表示 actual > expected，=0 表示相等，<0 表示 actual < expected
     */
    private int parseAndCompare(String actualValue, String expectedValue) {
        double actual = parseValue(actualValue);
        double expected = parseValue(expectedValue);
        return Double.compare(actual, expected);
    }

    /**
     * 解析值，支持百分比格式
     * @param value 字符串值
     * @return 解析后的double值
     */
    private double parseValue(String value) {
        if (value == null || value.isEmpty()) {
            return 0.0;
        }

        // 去除首尾空格
        value = value.trim();

        // 检查是否为百分比格式
        if (value.endsWith("%")) {
            // 去除百分号并转换为小数
            String numericPart = value.substring(0, value.length() - 1);
            return Double.parseDouble(numericPart) / 100.0;
        } else {
            // 普通数值格式
            return Double.parseDouble(value);
        }
    }

    /**
     * 根据targets字段对数据进行筛选
     *
     * @param entryList  原始数据列表
     * @param targets    目标字段列表
     * @param conditions
     * @return 筛选后的数据列表
     */
    private List<BiIdxDataVo> filterByTargets(List<BiIdxDataVo> entryList, List<String> targets, List<QueryCondition> conditions) {
        if (entryList == null || entryList.isEmpty()) {
            log.info("数据列表为空，返回空列表");
            return new ArrayList<>();
        }
        
        if (targets == null || targets.isEmpty()) {
            log.info("targets字段为空，返回原始数据");
            return entryList;
        }
        
        // 从conditions中提取所有的field值（提取公共逻辑）
        Set<String> conditionFields = extractFieldsFromConditions(conditions);
        
        // 检查targets是否包含星号
        boolean containsStar = targets.contains("*");
        
        if (containsStar) {
            // 当targets为星号时，保留idxConfCode为"productName"和"productType"的对象
            // 再加上从conditions中提取所有的field值对应的数据
            
            List<BiIdxDataVo> filteredList = entryList.stream()
                .filter(vo -> {
                    // 原有条件：idxConfCode为"productName"或"productType"
                    boolean defaultCondition = "productName".equals(vo.getIdxConfCode()) || "productType".equals(vo.getIdxConfCode())|| (vo.getIdxConfName() != null && vo.getIdxConfName().equals("taskId"));
                    
                    // 新增条件：idxConfCode在conditions的field中
                    boolean conditionFieldMatch = vo.getIdxConfCode() != null && conditionFields.contains(vo.getIdxConfCode());
                    
                    return defaultCondition || conditionFieldMatch;
                })
                .collect(Collectors.toList());
            
            log.info("targets包含星号，筛选productName、productType和conditions中的字段，筛选前: {}，筛选后: {}，conditions字段数量: {}", 
                    entryList.size(), filteredList.size(), conditionFields.size());
            return filteredList;
        } else {
            // 当targets不为星号，idxConfCode在目标字段列表(targets)中的数据，idxConfName为"taskId"的所有数据
            // 加上conditions中field等于idxConfCode的数据
            
            List<BiIdxDataVo> filteredList = entryList.stream()
                .filter(vo -> {
                    // 原有条件：idxConfCode在targets中 或 idxConfName为"taskId"
                    boolean originalCondition = (vo.getIdxConfCode() != null && targets.contains(vo.getIdxConfCode())) 
                                              || (vo.getIdxConfName() != null && vo.getIdxConfName().equals("taskId"));
                    
                    // 新增条件：idxConfCode在conditions的field中
                    boolean conditionFieldMatch = vo.getIdxConfCode() != null && conditionFields.contains(vo.getIdxConfCode());
                    
                    return originalCondition || conditionFieldMatch;
                })
                .collect(Collectors.toList());
            
            log.info("targets不包含星号，筛选targets和conditions中的对象，筛选前: {}，筛选后: {}，conditions字段数量: {}", 
                    entryList.size(), filteredList.size(), conditionFields.size());
            return filteredList;
        }
    }

    /**
     * 根据操作符应用SQL查询条件
     * @param queryWrapper 查询包装器
     * @param column 字段函数
     * @param operator 操作符
     * @param value 过滤值
     */
    private <T> void applyQueryCondition(LambdaQueryWrapper<BiIdxData> queryWrapper, 
                                        SFunction<BiIdxData, T> column, 
                                        String operator, 
                                        String value) {
        try {
            if (value == null) {
                value = "";
            }
            
            log.debug("应用SQL查询条件 - operator: {}, value: {}", operator, value);
            
            switch (operator.toLowerCase()) {
                case "eq":
                case "equals":
                case "=":
                    queryWrapper.eq(column, value);
                    break;
                    
                case "ne":
                case "not_equals":
                case "!=":
                    queryWrapper.ne(column, value);
                    break;
                    
                case "like":
                case "contains":
                    queryWrapper.like(column, value);
                    break;
                    
                case "not_like":
                case "not_contains":
                    queryWrapper.notLike(column, value);
                    break;
                    
                case "starts_with":
                case "prefix":
                    queryWrapper.likeRight(column, value);
                    break;
                    
                case "ends_with":
                case "suffix":
                    queryWrapper.likeLeft(column, value);
                    break;
                    
                case "gt":
                case ">":
                    queryWrapper.gt(column, value);
                    break;
                    
                case "gte":
                case ">=":
                    queryWrapper.ge(column, value);
                    break;
                    
                case "lt":
                case "<":
                    queryWrapper.lt(column, value);
                    break;
                    
                case "lte":
                case "<=":
                    queryWrapper.le(column, value);
                    break;
                    
                case "in":
                    // 支持逗号分隔的多值
                    List<String> inValues = Arrays.asList(value.split(","));
                    List<String> trimmedInValues = inValues.stream()
                            .map(String::trim)
                            .collect(Collectors.toList());
                    queryWrapper.in(column, trimmedInValues);
                    break;
                    
                case "not_in":
                    // 支持逗号分隔的多值
                    List<String> notInValues = Arrays.asList(value.split(","));
                    List<String> trimmedNotInValues = notInValues.stream()
                            .map(String::trim)
                            .collect(Collectors.toList());
                    queryWrapper.notIn(column, trimmedNotInValues);
                    break;
                    
                case "is_null":
                case "null":
                    queryWrapper.isNull(column);
                    break;
                    
                case "is_not_null":
                case "not_null":
                    queryWrapper.isNotNull(column);
                    break;
                    
                default:
                    log.warn("未支持的操作符: {}, 默认使用equals比较", operator);
                    queryWrapper.eq(column, value);
                    break;
            }
        } catch (Exception e) {
            log.error("SQL查询条件应用失败 - operator: {}, value: {}, error: {}", 
                    operator, value, e.getMessage());
            // 出错时不添加该条件，继续处理其他条件
        }
    }

    /**
     * 从conditions中提取所有的field值
     * @param conditions 查询条件列表
     * @return field值的集合
     */
    private Set<String> extractFieldsFromConditions(List<QueryCondition> conditions) {
        Set<String> conditionFields = new HashSet<>();
        if (conditions != null && !conditions.isEmpty()) {
            conditionFields = conditions.stream()
                .filter(condition -> condition.getField() != null)
                .map(QueryCondition::getField)
                .collect(Collectors.toSet());
            log.info("从conditions中提取到的field字段: {}", conditionFields);
        }
        return conditionFields;
    }
}
