package com.icarus.bi.service.impl;

import com.icarus.bi.entity.BiIdxDsconf;
import com.icarus.bi.mapper.BiIdxDsconfMapper;
import com.icarus.bi.service.IBiIdxDsconfService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 数据源配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@Service
public class BiIdxDsconfServiceImpl extends ServiceImpl<BiIdxDsconfMapper, BiIdxDsconf> implements IBiIdxDsconfService {

}
