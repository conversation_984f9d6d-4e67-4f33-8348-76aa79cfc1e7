package com.icarus.bi.service.impl;

import cn.hutool.core.util.StrUtil;
import com.icarus.bi.entity.BiCotResult;
import com.icarus.bi.dto.BiCotResultQueryDTO;
import com.icarus.bi.mapper.BiCotResultMapper;
import com.icarus.bi.service.IBiCotResultService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 抽取结果表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Slf4j
@Service
public class BiCotResultServiceImpl extends ServiceImpl<BiCotResultMapper, BiCotResult> implements IBiCotResultService {

    @Override
    @Transactional
    public List<BiCotResult> batchSaveResults(String taskId, Map<String, Object> extractElements) {
        try {
            List<BiCotResult> results = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now();

            for (Map.Entry<String, Object> entry : extractElements.entrySet()) {
                BiCotResult result = new BiCotResult();
                result.setTaskId(taskId);
                result.setElementField(entry.getKey());
                result.setElementName(entry.getKey()); // 可以根据需要设置更友好的名称
                result.setElementResult(entry.getValue() != null ? entry.getValue().toString() : "");
                result.setCreateTime(now);
                result.setUpdateTime(now);
                results.add(result);
            }

            boolean success = this.saveBatch(results);
            log.info("[批量保存抽取结果]完成, taskId:{}, 保存数量:{}, 结果:{}", taskId, results.size(), success);
            return results;

        } catch (Exception e) {
            log.error("[批量保存抽取结果]失败, taskId:{}, error:{}", taskId, e.getMessage(), e);
            throw new RuntimeException("批量保存抽取结果失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<BiCotResult> getResultsByTaskId(String taskId, String keyword) {
        try {
            LambdaQueryWrapper<BiCotResult> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BiCotResult::getTaskId, taskId);
            queryWrapper.orderByAsc(BiCotResult::getElementField);
            if (StrUtil.isNotBlank(keyword)) {
                queryWrapper.like(BiCotResult::getElementResult, keyword)
                        .or()
                        .like(BiCotResult::getElementField, keyword)
                        .or()
                        .like(BiCotResult::getElementName, keyword);
            }

            List<BiCotResult> results = this.list(queryWrapper);
            log.info("[根据任务ID查询抽取结果]成功, taskId:{}, 结果数量:{}", taskId, results.size());
            return results;

        } catch (Exception e) {
            log.error("[根据任务ID查询抽取结果]失败, taskId:{}, error:{}", taskId, e.getMessage(), e);
            throw new RuntimeException("查询抽取结果失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<BiCotResult> getResultsByTaskIdWithConditions(String taskId,
                                                              String elementField,
                                                              String elementName,
                                                              String elementResult,
                                                              String elementScopeExp) {
        try {
            log.info("[根据任务ID和条件查询抽取结果]开始, taskId:{}, elementField:{}, elementName:{}, elementResult:{}, elementScopeExp:{}",
                    taskId, elementField, elementName, elementResult, elementScopeExp);

            LambdaQueryWrapper<BiCotResult> queryWrapper = new LambdaQueryWrapper<>();

            // 任务ID必填条件
            queryWrapper.eq(BiCotResult::getTaskId, taskId);

            // 要素字段code模糊查询
            if (StrUtil.isNotBlank(elementField)) {
                queryWrapper.like(BiCotResult::getElementField, elementField);
            }

            // 要素字段Name模糊查询
            if (StrUtil.isNotBlank(elementName)) {
                queryWrapper.like(BiCotResult::getElementName, elementName);
            }

            // 要素提取结果模糊查询
            if (StrUtil.isNotBlank(elementResult)) {
                queryWrapper.like(BiCotResult::getElementResult, elementResult);
            }

            // 要素提取章节表达式模糊查询
            if (StrUtil.isNotBlank(elementScopeExp)) {
                queryWrapper.like(BiCotResult::getElementScopeExp, elementScopeExp);
            }

            // 按创建时间倒序排列
            queryWrapper.orderByDesc(BiCotResult::getCreateTime);

            List<BiCotResult> results = this.list(queryWrapper);
            log.info("[根据任务ID和条件查询抽取结果]成功, taskId:{}, 查询结果数量:{}", taskId, results.size());

            return results;

        } catch (Exception e) {
            log.error("[根据任务ID和条件查询抽取结果]失败, taskId:{}, error:{}", taskId, e.getMessage(), e);
            throw new RuntimeException("根据任务ID和条件查询抽取结果失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Page<BiCotResult> pageQueryResults(BiCotResultQueryDTO queryDTO) {
        try {
            log.info("[分页查询抽取结果]开始, queryDTO:{}", queryDTO);

            // 验证参数
            if (queryDTO == null) {
                throw new IllegalArgumentException("查询参数不能为空");
            }

            if (StrUtil.isBlank(queryDTO.getTaskId())) {
                throw new IllegalArgumentException("任务ID不能为空");
            }

            // 创建分页对象
            Page<BiCotResult> page = new Page<>(queryDTO.getCurrentPage(), queryDTO.getPageSize());

            // 构建查询条件
            LambdaQueryWrapper<BiCotResult> queryWrapper = new LambdaQueryWrapper<>();

            // 任务ID必填条件
            queryWrapper.eq(BiCotResult::getTaskId, queryDTO.getTaskId());

            // 要素字段code模糊查询
            if (StrUtil.isNotBlank(queryDTO.getElementField())) {
                queryWrapper.like(BiCotResult::getElementField, queryDTO.getElementField());
            }

            // 要素字段Name模糊查询
            if (StrUtil.isNotBlank(queryDTO.getElementName())) {
                queryWrapper.like(BiCotResult::getElementName, queryDTO.getElementName());
            }

            // 要素提取结果模糊查询
            if (StrUtil.isNotBlank(queryDTO.getElementResult())) {
                queryWrapper.like(BiCotResult::getElementResult, queryDTO.getElementResult());
            }

            // 要素提取章节表达式模糊查询
            if (StrUtil.isNotBlank(queryDTO.getElementScopeExp())) {
                queryWrapper.like(BiCotResult::getElementScopeExp, queryDTO.getElementScopeExp());
            }

            // 排序
            String orderBy = queryDTO.getValidOrderBy();
            String orderDirection = queryDTO.getValidOrderDirection();

            if ("asc".equals(orderDirection)) {
                switch (orderBy) {
                    case "createTime":
                        queryWrapper.orderByAsc(BiCotResult::getCreateTime);
                        break;
                    case "elementField":
                        queryWrapper.orderByAsc(BiCotResult::getElementField);
                        break;
                    case "elementName":
                        queryWrapper.orderByAsc(BiCotResult::getElementName);
                        break;
                    case "updateTime":
                        queryWrapper.orderByAsc(BiCotResult::getUpdateTime);
                        break;
                    default:
                        queryWrapper.orderByAsc(BiCotResult::getCreateTime);
                }
            } else {
                switch (orderBy) {
                    case "createTime":
                        queryWrapper.orderByDesc(BiCotResult::getCreateTime);
                        break;
                    case "elementField":
                        queryWrapper.orderByDesc(BiCotResult::getElementField);
                        break;
                    case "elementName":
                        queryWrapper.orderByDesc(BiCotResult::getElementName);
                        break;
                    case "updateTime":
                        queryWrapper.orderByDesc(BiCotResult::getUpdateTime);
                        break;
                    default:
                        queryWrapper.orderByDesc(BiCotResult::getCreateTime);
                }
            }

            Page<BiCotResult> result = this.page(page, queryWrapper);
            log.info("[分页查询抽取结果]成功, taskId:{}, 当前页:{}, 页大小:{}, 总记录数:{}, 排序:{}_{}",
                    queryDTO.getTaskId(), queryDTO.getCurrentPage(), queryDTO.getPageSize(),
                    result.getTotal(), orderBy, orderDirection);
            return result;

        } catch (Exception e) {
            log.error("[分页查询抽取结果]失败, queryDTO:{}, error:{}", queryDTO, e.getMessage(), e);
            throw new RuntimeException("[分页查询抽取结果]失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean deleteResultsByTaskId(String taskId) {
        try {
            LambdaQueryWrapper<BiCotResult> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BiCotResult::getTaskId, taskId);

            boolean success = this.remove(queryWrapper);
            log.info("[删除任务相关抽取结果]完成, taskId:{}, 结果:{}", taskId, success);
            return success;

        } catch (Exception e) {
            log.error("[删除任务相关抽取结果]失败, taskId:{}, error:{}", taskId, e.getMessage(), e);
            throw new RuntimeException("删除抽取结果失败: " + e.getMessage(), e);
        }
    }
}
