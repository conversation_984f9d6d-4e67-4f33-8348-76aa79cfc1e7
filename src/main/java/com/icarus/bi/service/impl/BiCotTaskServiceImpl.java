package com.icarus.bi.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.bi.entity.BiCotRule;
import com.icarus.bi.entity.BiCotTask;
import com.icarus.bi.dto.BiCotTaskQueryDTO;
import com.icarus.bi.enums.AuditStatusEnum;
import com.icarus.bi.enums.TaskStatusEnum;
import com.icarus.bi.exception.BiCotRuleNotActiveException;
import com.icarus.bi.mapper.BiCotTaskMapper;
import com.icarus.bi.service.IBiCotRuleService;
import com.icarus.bi.service.IBiCotTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <p>
 * 抽取规则配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Slf4j
@Service
public class BiCotTaskServiceImpl extends ServiceImpl<BiCotTaskMapper, BiCotTask> implements IBiCotTaskService {

    @Autowired
    private IBiCotRuleService biCotRuleService;

    @Override
    public boolean saveTask(BiCotTask biCotTask) {
        try {
            LocalDateTime now = LocalDateTime.now();
            // ID由MyBatis-Plus自动生成，无需手动设置
            biCotTask.setCreateTime(now);
            biCotTask.setUpdateTime(now);
            if (StrUtil.isBlank(biCotTask.getTaskStatus())) {
                biCotTask.setTaskStatus(TaskStatusEnum.TODO.getCode());
            }
            if (StrUtil.isBlank(biCotTask.getAuditStatus())) {
                biCotTask.setAuditStatus(AuditStatusEnum.UNDO.getCode());
            }
            if (StrUtil.isBlank(biCotTask.getRuleId())) {
                BiCotRule activeRule = biCotRuleService.getActiveRule();
                if (Objects.isNull(activeRule)) {
                    log.warn("[新增COT任务]未找到启用的抽取规则");
                    throw new BiCotRuleNotActiveException("未找到启用的抽取规则, 请先启用规则");
                }
                biCotTask.setRuleId(activeRule.getId());
                biCotTask.setRuleName(activeRule.getName());
                biCotTask.setCanvasId(activeRule.getCanvasId());
            }
            if (StrUtil.isBlank(biCotTask.getSourceFileKey())){
                //校验sourceFileKey字段不能为空
                log.error("[新增COT任务]失败, sourceFileKey字段不能为空");
                throw new RuntimeException("[新增COT任务]失败, sourceFileKey字段不能为空");
            }

            if (StrUtil.isNotBlank(biCotTask.getExtraAttrs())
                    && !JSONUtil.isTypeJSON(biCotTask.getExtraAttrs())){
                //校验extraAttrs字段须为JSON
                log.error("[新增COT任务]失败, extraAttrs字段不是JSON格式");
                throw new RuntimeException("[新增COT任务]失败, extraAttrs字段不是JSON格式");
            }

            boolean result = this.save(biCotTask);
            log.info("[新增COT任务]成功, id:{}, ruleId:{}, ruleName:{}", biCotTask.getId(), biCotTask.getRuleId(), biCotTask.getRuleName());
            return result;
        } catch (Exception e) {
            log.error("[新增COT任务]失败, ruleId:{}, ruleName:{}, error:{}", biCotTask.getRuleId(), biCotTask.getRuleName(), e.getMessage());
            throw new RuntimeException("[新增COT任务]失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean updateTask(BiCotTask biCotTask) {
        try {
            BiCotTask existing = this.getById(biCotTask.getId());
            if (existing == null) {
                log.error("[修改COT任务]失败, 未找到该记录, id:{}", biCotTask.getId());
                throw new RuntimeException("[修改COT任务]失败, 未找到该记录");
            }

            biCotTask.setUpdateTime(LocalDateTime.now());
            // 保留原有的创建时间
            biCotTask.setCreateTime(existing.getCreateTime());
            boolean result = this.updateById(biCotTask);
            log.info("[修改COT任务]成功, id:{}, ruleId:{}, ruleName:{}", biCotTask.getId(), biCotTask.getRuleId(), biCotTask.getRuleName());
            return result;
        } catch (Exception e) {
            log.error("[修改COT任务]失败, id:{}, error:{}", biCotTask.getId(), e.getMessage());
            throw new RuntimeException("[修改COT任务]失败: " + e.getMessage(), e);
        }
    }

    @Override
    public BiCotTask getTaskById(String id) {
        try {
            BiCotTask biCotTask = this.getById(id);
            if (biCotTask == null) {
                log.warn("[获取COT任务详情]未找到记录, id:{}", id);
            }
            return biCotTask;
        } catch (Exception e) {
            log.error("[获取COT任务详情]失败, id:{}, error:{}", id, e.getMessage());
            throw new RuntimeException("[获取COT任务详情]失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Page<BiCotTask> pageQueryTask(BiCotTaskQueryDTO queryDTO) {
        try {
            log.info("[分页查询COT任务-DTO]开始, queryDTO:{}", queryDTO);

            // 验证参数
            if (queryDTO == null) {
                queryDTO = new BiCotTaskQueryDTO();
            }

            // 验证时间范围
            if (!queryDTO.isValidTimeRange()) {
                throw new IllegalArgumentException("时间范围设置不合理");
            }

            // 创建分页对象
            Page<BiCotTask> page = new Page<>(queryDTO.getCurrentPage(), queryDTO.getPageSize());

            // 构建查询条件
            LambdaQueryWrapper<BiCotTask> queryWrapper = new LambdaQueryWrapper<>();

            // 基础条件
            if (StrUtil.isNotBlank(queryDTO.getRuleId())) {
                queryWrapper.eq(BiCotTask::getRuleId, queryDTO.getRuleId());
            }
            if (StrUtil.isNotBlank(queryDTO.getRuleName())) {
                queryWrapper.like(BiCotTask::getRuleName, queryDTO.getRuleName());
            }
            if (StrUtil.isNotBlank(queryDTO.getCanvasId())) {
                queryWrapper.eq(BiCotTask::getCanvasId, queryDTO.getCanvasId());
            }
            if (StrUtil.isNotBlank(queryDTO.getTaskStatus())) {
                queryWrapper.eq(BiCotTask::getTaskStatus, queryDTO.getTaskStatus());
            }
            if (StrUtil.isNotBlank(queryDTO.getAuditStatus())) {
                queryWrapper.eq(BiCotTask::getAuditStatus, queryDTO.getAuditStatus());
            }

            // 扩展属性查询（模糊查询JSON内容）
            if (StrUtil.isNotBlank(queryDTO.getExtraAttrs())) {
                queryWrapper.like(BiCotTask::getExtraAttrs, queryDTO.getExtraAttrs());
            }

            // 源文件key查询（模糊查询）
            if (StrUtil.isNotBlank(queryDTO.getSourceFileKey())) {
                queryWrapper.like(BiCotTask::getSourceFileKey, queryDTO.getSourceFileKey());
            }

            // 创建时间范围
            if (queryDTO.getCreateTimeStart() != null) {
                queryWrapper.ge(BiCotTask::getCreateTime, queryDTO.getCreateTimeStart());
            }
            if (queryDTO.getCreateTimeEnd() != null) {
                queryWrapper.le(BiCotTask::getCreateTime, queryDTO.getCreateTimeEnd());
            }

            // 任务结束时间范围
            if (queryDTO.getTaskEndTimeStart() != null) {
                queryWrapper.ge(BiCotTask::getTaskEndTime, queryDTO.getTaskEndTimeStart());
            }
            if (queryDTO.getTaskEndTimeEnd() != null) {
                queryWrapper.le(BiCotTask::getTaskEndTime, queryDTO.getTaskEndTimeEnd());
            }

            // 排序 - 默认按创建时间倒序
            String orderBy = queryDTO.getValidOrderBy();
            String orderDirection = queryDTO.getValidOrderDirection();

            if ("asc".equals(orderDirection)) {
                switch (orderBy) {
                    case "createTime":
                        queryWrapper.orderByAsc(BiCotTask::getCreateTime);
                        break;
                    case "taskEndTime":
                        queryWrapper.orderByAsc(BiCotTask::getTaskEndTime);
                        break;
                    default:
                        queryWrapper.orderByAsc(BiCotTask::getCreateTime);
                }
            } else {
                switch (orderBy) {
                    case "createTime":
                        queryWrapper.orderByDesc(BiCotTask::getCreateTime);
                        break;
                    case "taskEndTime":
                        queryWrapper.orderByDesc(BiCotTask::getTaskEndTime);
                        break;
                    default:
                        queryWrapper.orderByDesc(BiCotTask::getCreateTime);
                }
            }

            Page<BiCotTask> result = this.page(page, queryWrapper);
            log.info("[分页查询COT任务-DTO]成功, 当前页:{}, 页大小:{}, 总记录数:{}, 排序:{}_{}",
                    queryDTO.getCurrentPage(), queryDTO.getPageSize(), result.getTotal(), orderBy, orderDirection);
            return result;

        } catch (Exception e) {
            log.error("[分页查询COT任务-DTO]失败, queryDTO:{}, error:{}", queryDTO, e.getMessage(), e);
            throw new RuntimeException("[分页查询COT任务-DTO]失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean updateTaskStatus(String id, String taskStatus, String errorReason) {
        try {
            BiCotTask biCotTask = this.getById(id);
            if (biCotTask == null) {
                log.error("[更新任务执行状态]失败, 未找到该记录, id:{}", id);
                throw new RuntimeException("[更新任务执行状态]失败, 未找到该记录");
            }
            biCotTask.setTaskStatus(taskStatus);
            biCotTask.setUpdateTime(LocalDateTime.now());
            if (StrUtil.isNotBlank(errorReason)) {
                biCotTask.setErrorReason(errorReason);
            }

            // 根据状态设置执行时间
            if (TaskStatusEnum.COT_DOING.getCode().equals(taskStatus)) {
                biCotTask.setTaskStartTime(LocalDateTime.now());
            } else if (TaskStatusEnum.IDX_DONE.getCode().equals(taskStatus) || TaskStatusEnum.ERROR.getCode().equals(taskStatus)) {
                biCotTask.setTaskEndTime(LocalDateTime.now());
            }

            boolean result = this.updateById(biCotTask);
            log.info("[更新任务执行状态]成功, id:{}, taskStatus:{}", id, taskStatus);
            return result;
        } catch (Exception e) {
            log.error("[更新任务执行状态]失败, id:{}, taskStatus:{}, error:{}", id, taskStatus, e.getMessage());
            throw new RuntimeException("[更新任务执行状态]失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean updateAuditStatus(String id, String auditStatus) {
        try {
            BiCotTask biCotTask = this.getById(id);
            if (biCotTask == null) {
                log.error("[更新任务审核状态]失败, 未找到该记录, id:{}", id);
                throw new RuntimeException("[更新任务审核状态]失败, 未找到该记录");
            }
            biCotTask.setAuditStatus(auditStatus);
            biCotTask.setUpdateTime(LocalDateTime.now());
            boolean result = this.updateById(biCotTask);
            log.info("[更新任务审核状态]成功, id:{}, auditStatus:{}", id, auditStatus);
            return result;
        } catch (Exception e) {
            log.error("[更新任务审核状态]失败, id:{}, auditStatus:{}, error:{}", id, auditStatus, e.getMessage());
            throw new RuntimeException("[更新任务审核状态]失败: " + e.getMessage(), e);
        }
    }
    @Override
    public BiCotTask getTaskByRuleId(String ruleId) {
        try {
            LambdaQueryWrapper<BiCotTask> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BiCotTask::getRuleId, ruleId);
            queryWrapper.orderByDesc(BiCotTask::getCreateTime);
            BiCotTask biCotTask = this.getOne(queryWrapper);
            if (biCotTask == null) {
                log.warn("[根据抽取规则ID查询任务]未找到记录, ruleId:{}", ruleId);
            }
            return biCotTask;
        } catch (Exception e) {
            log.error("[根据抽取规则ID查询任务]失败, ruleId:{}, error:{}", ruleId, e.getMessage());
            throw new RuntimeException("[根据抽取规则ID查询任务]失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Page<BiCotTask> getTasksByCanvasId(String canvasId, int currentPage, int pageSize) {
        try {
            Page<BiCotTask> page = new Page<>(currentPage, pageSize);
            LambdaQueryWrapper<BiCotTask> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BiCotTask::getCanvasId, canvasId);
            queryWrapper.orderByDesc(BiCotTask::getCreateTime);

            Page<BiCotTask> result = this.page(page, queryWrapper);
            log.info("[根据画布ID查询任务列表]成功, canvasId:{}, 总记录数:{}", canvasId, result.getTotal());
            return result;
        } catch (Exception e) {
            log.error("[根据画布ID查询任务列表]失败, canvasId:{}, error:{}", canvasId, e.getMessage());
            throw new RuntimeException("[根据画布ID查询任务列表]失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean deleteTask(String id) {
        try {
            BiCotTask biCotTask = this.getById(id);
            if (biCotTask == null) {
                log.error("[删除COT任务]失败, 未找到该记录, id:{}", id);
                throw new RuntimeException("[删除COT任务]失败, 未找到该记录");
            }
            boolean result = this.removeById(id);
            log.info("[删除COT任务]成功, id:{}, ruleId:{}", id, biCotTask.getRuleId());
            return result;
        } catch (Exception e) {
            log.error("[删除COT任务]失败, id:{}, error:{}", id, e.getMessage());
            throw new RuntimeException("[删除COT任务]失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Page<BiCotTask> getTasksByStatus(String taskStatus, int currentPage, int pageSize) {
        try {
            Page<BiCotTask> page = new Page<>(currentPage, pageSize);
            LambdaQueryWrapper<BiCotTask> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BiCotTask::getTaskStatus, taskStatus);
            queryWrapper.orderByDesc(BiCotTask::getCreateTime);

            Page<BiCotTask> result = this.page(page, queryWrapper);
            log.info("[根据执行状态查询任务列表]成功, taskStatus:{}, 总记录数:{}", taskStatus, result.getTotal());
            return result;
        } catch (Exception e) {
            log.error("[根据执行状态查询任务列表]失败, taskStatus:{}, error:{}", taskStatus, e.getMessage());
            throw new RuntimeException("[根据执行状态查询任务列表]失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean startTask(String id) {
        try {
            BiCotTask biCotTask = this.getById(id);
            if (biCotTask == null) {
                log.error("[启动任务执行]失败, 未找到该记录, id:{}", id);
                throw new RuntimeException("[启动任务执行]失败, 未找到该记录");
            }
            biCotTask.setTaskStatus(TaskStatusEnum.COT_DOING.getCode());
            biCotTask.setTaskStartTime(LocalDateTime.now());
            biCotTask.setUpdateTime(LocalDateTime.now());
            boolean result = this.updateById(biCotTask);
            log.info("[启动任务执行]成功, id:{}, ruleId:{}", id, biCotTask.getRuleId());
            return result;
        } catch (Exception e) {
            log.error("[启动任务执行]失败, id:{}, error:{}", id, e.getMessage());
            throw new RuntimeException("[启动任务执行]失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean finishTask(String id) {
        try {
            BiCotTask biCotTask = this.getById(id);
            if (biCotTask == null) {
                log.error("[完成任务执行]失败, 未找到该记录, id:{}", id);
                throw new RuntimeException("[完成任务执行]失败, 未找到该记录");
            }
            biCotTask.setTaskStatus(TaskStatusEnum.IDX_DONE.getCode());
            biCotTask.setTaskEndTime(LocalDateTime.now());
            biCotTask.setUpdateTime(LocalDateTime.now());
            boolean result = this.updateById(biCotTask);
            log.info("[完成任务执行]成功, id:{}, ruleId:{}", id, biCotTask.getRuleId());
            return result;
        } catch (Exception e) {
            log.error("[完成任务执行]失败, id:{}, error:{}", id, e.getMessage());
            throw new RuntimeException("[完成任务执行]失败: " + e.getMessage(), e);
        }
    }
}
