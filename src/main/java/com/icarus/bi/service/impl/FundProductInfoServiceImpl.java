package com.icarus.bi.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icarus.bi.entity.FundProductInfo;
import com.icarus.bi.mapper.FundProductInfoMapper;
import com.icarus.bi.service.IFundProductInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 基金产品信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
@Slf4j
@Service
public class FundProductInfoServiceImpl extends ServiceImpl<FundProductInfoMapper, FundProductInfo> implements IFundProductInfoService {

    @Override
    public List<FundProductInfo> getByTaskId(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            log.warn("[查询基金产品信息]任务ID为空");
            return CollectionUtil.newArrayList();
        }
        
        try {
            log.info("[查询基金产品信息]开始, taskId:{}", taskId);
            LambdaQueryWrapper<FundProductInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FundProductInfo::getTaskId, taskId);
            List<FundProductInfo> result = baseMapper.selectList(queryWrapper);
            log.info("[查询基金产品信息]完成, taskId:{}, 查询到{}条记录", taskId, result.size());
            return result;
        } catch (Exception e) {
            log.error("[查询基金产品信息]失败, taskId:{}, error:{}", taskId, e.getMessage(), e);
            throw new RuntimeException("查询基金产品信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<FundProductInfo> getByProductNameLike(String productName) {
        if (StrUtil.isBlank(productName)) {
            log.warn("[根据产品名称查询基金产品信息]产品名称为空");
            return CollectionUtil.newArrayList();
        }
        
        try {
            log.info("[根据产品名称查询基金产品信息]开始, productName:{}", productName);
            LambdaQueryWrapper<FundProductInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.like(FundProductInfo::getProductName, productName);
            List<FundProductInfo> result = baseMapper.selectList(queryWrapper);
            log.info("[根据产品名称查询基金产品信息]完成, productName:{}, 查询到{}条记录", productName, result.size());
            return result;
        } catch (Exception e) {
            log.error("[根据产品名称查询基金产品信息]失败, productName:{}, error:{}", productName, e.getMessage(), e);
            throw new RuntimeException("根据产品名称查询基金产品信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<FundProductInfo> getByProductType(String productType) {
        if (StrUtil.isBlank(productType)) {
            log.warn("[根据产品类型查询基金产品信息]产品类型为空");
            return CollectionUtil.newArrayList();
        }
        
        try {
            log.info("[根据产品类型查询基金产品信息]开始, productType:{}", productType);
            LambdaQueryWrapper<FundProductInfo> eq = new LambdaQueryWrapper<FundProductInfo>().eq(FundProductInfo::getProductType, productType);
            List<FundProductInfo> result = baseMapper.selectList(eq);
            log.info("[根据产品类型查询基金产品信息]完成, productType:{}, 查询到{}条记录", productType, result.size());
            return result;
        } catch (Exception e) {
            log.error("[根据产品类型查询基金产品信息]失败, productType:{}, error:{}", productType, e.getMessage(), e);
            throw new RuntimeException("根据产品类型查询基金产品信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveFundProductInfo(FundProductInfo fundProductInfo) {
        if (fundProductInfo == null) {
            log.warn("[保存基金产品信息]基金产品信息对象为空");
            return false;
        }
        
        try {
            log.info("[保存基金产品信息]开始, taskId:{}, productName:{}", 
                    fundProductInfo.getTaskId(), fundProductInfo.getProductName());
            
            // 设置创建时间和更新时间
            LocalDateTime now = LocalDateTime.now();
            fundProductInfo.setCreatedTime(now);
            fundProductInfo.setUpdatedTime(now);
            
            boolean result = save(fundProductInfo);
            log.info("[保存基金产品信息]完成, taskId:{}, productName:{}, result:{}", 
                    fundProductInfo.getTaskId(), fundProductInfo.getProductName(), result);
            return result;
        } catch (Exception e) {
            log.error("[保存基金产品信息]失败, taskId:{}, productName:{}, error:{}", 
                    fundProductInfo.getTaskId(), fundProductInfo.getProductName(), e.getMessage(), e);
            throw new RuntimeException("保存基金产品信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveFundProductInfo(List<FundProductInfo> fundProductInfoList) {
        if (CollectionUtil.isEmpty(fundProductInfoList)) {
            log.warn("[批量保存基金产品信息]基金产品信息列表为空");
            return false;
        }

        try {
            log.info("[批量保存基金产品信息]开始, 数量:{}", fundProductInfoList.size());

            // 设置创建时间和更新时间
            LocalDateTime now = LocalDateTime.now();
            fundProductInfoList.forEach(item -> {
                item.setCreatedTime(now);
                item.setUpdatedTime(now);
            });

            
            boolean result = insertCount > 0;
            log.info("[批量保存基金产品信息]完成, 数量:{}, 插入记录数:{}, result:{}",
                    fundProductInfoList.size(), insertCount, result);
            return result;
        } catch (Exception e) {
            log.error("[批量保存基金产品信息]失败, 数量:{}, error:{}", fundProductInfoList.size(), e.getMessage(), e);
            throw new RuntimeException("批量保存基金产品信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByTaskId(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            log.warn("[根据任务ID删除基金产品信息]任务ID为空");
            return false;
        }
        
        try {
            log.info("[根据任务ID删除基金产品信息]开始, taskId:{}", taskId);
            int deleteCount = baseMapper.delete(new LambdaQueryWrapper<FundProductInfo>().eq(FundProductInfo::getTaskId, taskId));
            boolean result = deleteCount > 0;
            log.info("[根据任务ID删除基金产品信息]完成, taskId:{}, 删除记录数:{}, result:{}", 
                    taskId, deleteCount, result);
            return result;
        } catch (Exception e) {
            log.error("[根据任务ID删除基金产品信息]失败, taskId:{}, error:{}", taskId, e.getMessage(), e);
            throw new RuntimeException("根据任务ID删除基金产品信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFundProductInfo(FundProductInfo fundProductInfo) {
        if (fundProductInfo == null || fundProductInfo.getId() == null) {
            log.warn("[更新基金产品信息]基金产品信息对象为空或ID为空");
            return false;
        }
        
        try {
            log.info("[更新基金产品信息]开始, id:{}, taskId:{}, productName:{}", 
                    fundProductInfo.getId(), fundProductInfo.getTaskId(), fundProductInfo.getProductName());
            
            // 设置更新时间
            fundProductInfo.setUpdatedTime(LocalDateTime.now());
            
            boolean result = updateById(fundProductInfo);
            log.info("[更新基金产品信息]完成, id:{}, taskId:{}, productName:{}, result:{}", 
                    fundProductInfo.getId(), fundProductInfo.getTaskId(), fundProductInfo.getProductName(), result);
            return result;
        } catch (Exception e) {
            log.error("[更新基金产品信息]失败, id:{}, taskId:{}, productName:{}, error:{}", 
                    fundProductInfo.getId(), fundProductInfo.getTaskId(), fundProductInfo.getProductName(), e.getMessage(), e);
            throw new RuntimeException("更新基金产品信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public FundProductInfo getById(Long id) {
        if (id == null) {
            log.warn("[根据ID查询基金产品信息]ID为空");
            return null;
        }
        
        try {
            log.info("[根据ID查询基金产品信息]开始, id:{}", id);
            FundProductInfo result = super.getById(id);
            log.info("[根据ID查询基金产品信息]完成, id:{}, found:{}", id, result != null);
            return result;
        } catch (Exception e) {
            log.error("[根据ID查询基金产品信息]失败, id:{}, error:{}", id, e.getMessage(), e);
            throw new RuntimeException("根据ID查询基金产品信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean existsByTaskId(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            return false;
        }
        
        try {
            LambdaQueryWrapper<FundProductInfo> wrapper = new LambdaQueryWrapper<FundProductInfo>()
                    .eq(FundProductInfo::getTaskId, taskId)
                    .last("LIMIT 1");
            
            FundProductInfo result = getOne(wrapper);
            return result != null;
        } catch (Exception e) {
            log.error("[检查任务ID是否存在基金产品信息]失败, taskId:{}, error:{}", taskId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public long countByTaskId(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            return 0L;
        }
        
        try {
            LambdaQueryWrapper<FundProductInfo> wrapper = new LambdaQueryWrapper<FundProductInfo>()
                    .eq(FundProductInfo::getTaskId, taskId);
            
            return count(wrapper);
        } catch (Exception e) {
            log.error("[统计任务ID对应的基金产品信息数量]失败, taskId:{}, error:{}", taskId, e.getMessage(), e);
            return 0L;
        }
    }
}
