package com.icarus.bi.service.external;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.icarus.bi.config.AppConfigs;
import com.icarus.bi.util.AipApiClientUtil;
import com.icarus.common.minio.MinioUtil;
import jakarta.annotation.Resource;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * Agent平台COT接口调用服务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Slf4j
@Service
public class AgentCotService {

    @Resource
    private MinioUtil minioUtil;

    @Value("${minio.agent-bucket-name}")
    private String agentBucketName;

    @Resource
    private RestTemplate restTemplate;

    /**
     * 文件大小限制常量
     */
    private static final long MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
    private static final long MAX_FILE_SIZE_MB = MAX_FILE_SIZE / (1024 * 1024);
    /**
     * 调用Agent平台COT接口抽取要素
     *
     * @param canvasId 画布ID
     * @param sourceFileKey minio文件地址
     * @return 抽取结果JSON字符串
     */
    public String extractElements(String canvasId, String sourceFileKey) {
        try {
            log.info("[调用Agent平台COT接口]开始, canvasId:{}", canvasId);
            
            // 构建请求参数
            Map<String, Object> queryParam = new HashMap<>();
            queryParam.put("canvas_id", canvasId);
            String endpointUrl = AppConfigs.aip().getUrl() + "/aip/endpoint/get";
            // 调用Agent平台COT接口
            String response = HttpUtil.get(endpointUrl, queryParam);

            JSONObject jsonObject = JSONUtil.parseObj(response);
            if (jsonObject.getInt("code") != 200) {
                log.error("获取语料切片接口失败，url:{},response: {}",endpointUrl, response);
                throw new RuntimeException("获取语料切片接口失败");
            }
            JSONObject data = jsonObject.getJSONObject("data");

            if (Objects.isNull(data)) {
                log.error("[调用Agent平台根据画布id获取endpoint]失败,url:{} canvasId:{}, error:{}",
                        endpointUrl, canvasId, "endpointResult is null || endpointResult is not JSONObject");
                throw new RuntimeException("调用Agent平台根据画布id获取endpoint: endpointResult is null|| endpointResult is not JSONObject");
            }
            EndpointResult bean = JSONUtil.toBean(JSONUtil.toJsonStr(data), EndpointResult.class);

            String canvasApi = "/" + bean.getSubsystem() + "/" + bean.getModule() + "/" + bean.getPath();

            if (StrUtil.isBlank(sourceFileKey)) {
                log.error("[调用Agent平台COT接口]未找到minio文件地址, sourceFileKey:{}", sourceFileKey);
                throw new RuntimeException("未找到minio文件地址");
            }

            // 使用form-data方式发送文件
            String canvasResponse = callApiWithFile(canvasApi, sourceFileKey);
            if (StrUtil.isBlank(canvasResponse) || !JSONUtil.isTypeJSON(canvasResponse)) {
                log.error("[调用Agent平台COT接口]失败,url:{} canvasId:{}, error:{}", canvasApi, canvasId,
                        "canvasResult is null || canvasResult is not JSONObject");
                throw new RuntimeException("调用Agent平台COT接口失败: canvasResult is null|| canvasResult is not JSONObject");
            }

            log.info("[调用Agent平台COT接口]成功, canvasId:{}, responseLength:{}",
                    canvasId, canvasResponse != null ? canvasResponse.length() : 0);
            
            return canvasResponse;
            
        } catch (Exception e) {
            log.error("[调用Agent平台COT接口]失败, canvasId:{}, error:{}", canvasId, e.getMessage(), e);
            throw new RuntimeException("调用Agent平台COT接口失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析抽取结果
     * 
     * @param extractResult 抽取结果JSON
     * @return 解析后的要素Map
     */
    public Map<String, Object> parseExtractResult(String extractResult) {
        try {

            Object o = AipApiClientUtil.parseAipResult(extractResult);
            if (Objects.isNull(o)){
                log.warn("[解析抽取结果]未找到任何要素, extractResult:{}", extractResult);
                return new HashMap<>();
            }
            Map<String, Object> stringObjectMap = jsonArrayToMapStream(o);
            return stringObjectMap;
        } catch (Exception e) {
            log.error("[解析抽取结果]失败, error:{}", e.getMessage(), e);
            throw new RuntimeException("解析抽取结果失败: " + e.getMessage(), e);
        }
    }



    /**
     * 验证文件大小是否超过限制
     *
     * @param fileBytes 文件字节数组
     * @param fileUrl 文件URL（用于日志）
     */
    private void validateFileSize(byte[] fileBytes, String fileUrl) {
        if (fileBytes == null) {
            throw new RuntimeException("文件内容为空");
        }

        long fileSizeBytes = fileBytes.length;
        double fileSizeMB = fileSizeBytes / (1024.0 * 1024.0);

        log.info("[验证文件大小]文件大小: {:.2f}MB, 限制: {}MB, fileUrl:{}", fileSizeMB, MAX_FILE_SIZE_MB, fileUrl);

        if (fileSizeBytes > MAX_FILE_SIZE) {
            String errorMsg = String.format("文件大小超过限制: %.2fMB > %dMB, fileUrl: %s",
                    fileSizeMB, MAX_FILE_SIZE_MB, fileUrl);
            log.error("[验证文件大小]失败, {}", errorMsg);
            throw new RuntimeException(errorMsg);
        }

        log.info("[验证文件大小]通过, 文件大小: {:.2f}MB", fileSizeMB);
    }

    /**
     * 使用form-data方式调用API，发送文件
     *
     * @param apiPath API路径
     * @param fileUrl 文件URL地址
     * @return 响应结果
     */
    private String callApiWithFile(String apiPath, String fileUrl) {
        try {
            log.info("[调用API发送文件]开始, apiPath:{}, fileUrl:{}", apiPath, fileUrl);

            // 1. 从URL下载文件内容
            byte[] fileBytes = downloadFileFromUrl(fileUrl);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            // 构建文件资源
            ByteArrayResource fileResource = new ByteArrayResource(fileBytes) {
                @Override
                public String getFilename() {
                    // 从URL中提取文件名
                    String fileName = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
                    return fileName;
                }
            };

            // 构建form-data参数
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("file", fileResource);

            // 创建请求实体
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // 构建完整URL
            String fullUrl = AppConfigs.aip().getSysReqUrl() + apiPath;

            log.info("[调用API发送文件]构建完整URL:{}", fullUrl);
            // 发送POST请求
            ResponseEntity<String> response = restTemplate.postForEntity(fullUrl, requestEntity, String.class);

            log.info("请求结果:{}",JSONUtil.toJsonStr(response));

            String responseBody = response.getBody();
            log.info("[调用API发送文件]成功, apiPath:{}, responseLength:{}", apiPath,
                    responseBody != null ? responseBody.length() : 0);

            return responseBody;

        } catch (Exception e) {
            log.error("[调用API发送文件]失败, api:{}, fileUrl:{}, error:{}", apiPath, fileUrl, e.getMessage(), e);
            throw new RuntimeException("调用API发送文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从URL下载文件内容
     *
     * @param fileUrl 文件URL
     * @return 文件字节数组
     */
    private byte[] downloadFileFromUrl(String fileUrl) {
        try {
            log.info("[下载文件]开始, fileUrl:{}", fileUrl);

            try (InputStream inputStream = minioUtil.downloadFile(agentBucketName, fileUrl);
                 ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

                byte[] buffer = new byte[8192]; // 增大缓冲区
                int bytesRead;
                long totalBytesRead = 0;

                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    totalBytesRead += bytesRead;

                    // 在下载过程中检查大小，避免下载过大文件
                    if (totalBytesRead > MAX_FILE_SIZE) {
                        double sizeMB = totalBytesRead / (1024.0 * 1024.0);
                        String errorMsg = String.format("下载过程中发现文件过大: %.2fMB > %dMB, fileUrl: %s",
                                sizeMB, MAX_FILE_SIZE_MB, fileUrl);
                        log.error("[下载文件]失败, {}", errorMsg);
                        throw new RuntimeException(errorMsg);
                    }

                    outputStream.write(buffer, 0, bytesRead);
                }

                byte[] fileBytes = outputStream.toByteArray();
                double fileSizeMB = fileBytes.length / (1024.0 * 1024.0);
                log.info("[下载文件]成功, fileUrl:{}, fileSize:{:.2f}MB ({} bytes)",
                        fileUrl, fileSizeMB, fileBytes.length);

                return fileBytes;
            }

        } catch (Exception e) {
            log.error("[下载文件]失败, fileUrl:{}, error:{}", fileUrl, e.getMessage(), e);
            throw new RuntimeException("下载文件失败: " + e.getMessage(), e);
        }
    }

    @Getter
    @Setter
    public static class EndpointResult{
        private String id;
        private String project;
        private String subsystem;
        private String module;
        private String description;
        private String type;
        private String templateId;
        private String reqParam;
        private String path;
        private LocalDateTime createTime;
        private LocalDateTime updateTime;
    }

    /**
     * jsonArray转map
     * @param jsonArrayStr
     * @return
     */
    public static Map<String, Object> jsonArrayToMapStream(Object jsonArrayStr) {
        JSONArray jsonArray = JSONUtil.parseArray(jsonArrayStr);

        return jsonArray.stream()
                .map(obj -> (JSONObject) obj)
                .flatMap(json -> json.entrySet().stream())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existing, replacement) -> replacement // 如果有重复key，使用后面的值
                ));
    }
}
