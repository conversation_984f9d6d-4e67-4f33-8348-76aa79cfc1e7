package com.icarus.bi.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.bi.entity.BiCotTask;
import com.icarus.bi.dto.BiCotTaskQueryDTO;

/**
 * <p>
 * 抽取规则配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
public interface IBiCotTaskService extends IService<BiCotTask> {

    /**
     * 新增COT任务
     * @param biCotTask COT任务
     * @return 是否成功
     */
    boolean saveTask(BiCotTask biCotTask);

    /**
     * 修改COT任务
     * @param biCotTask COT任务
     * @return 是否成功
     */
    boolean updateTask(BiCotTask biCotTask);

    /**
     * 根据ID获取COT任务详情
     * @param id 主键ID
     * @return COT任务
     */
    BiCotTask getTaskById(String id);


    /**
     * 分页查询COT任务（新方法，支持时间范围和扩展属性查询）
     * @param queryDTO 查询参数DTO
     * @return 分页结果
     */
    Page<BiCotTask> pageQueryTask(BiCotTaskQueryDTO queryDTO);

    /**
     * 更新任务执行状态
     * @param id 主键ID
     * @param taskStatus 新状态
     * @return 是否成功
     */
    boolean updateTaskStatus(String id, String taskStatus, String errorReason);

    /**
     * 更新任务审核状态
     * @param id 主键ID
     * @param auditStatus 审核状态
     * @return 是否成功
     */
    boolean updateAuditStatus(String id, String auditStatus);

    /**
     * 根据抽取规则ID查询任务
     * @param ruleId 抽取规则ID
     * @return COT任务
     */
    BiCotTask getTaskByRuleId(String ruleId);

    /**
     * 根据画布ID查询任务列表
     * @param canvasId 画布ID
     * @param currentPage 当前页
     * @param pageSize 页大小
     * @return 分页结果
     */
    Page<BiCotTask> getTasksByCanvasId(String canvasId, int currentPage, int pageSize);

    /**
     * 删除COT任务
     * @param id 主键ID
     * @return 是否成功
     */
    boolean deleteTask(String id);

    /**
     * 根据执行状态查询任务列表
     * @param taskStatus 执行状态
     * @param currentPage 当前页
     * @param pageSize 页大小
     * @return 分页结果
     */
    Page<BiCotTask> getTasksByStatus(String taskStatus, int currentPage, int pageSize);

    /**
     * 启动任务执行
     * @param id 主键ID
     * @return 是否成功
     */
    boolean startTask(String id);

    /**
     * 完成任务执行
     * @param id 主键ID
     * @return 是否成功
     */
    boolean finishTask(String id);
}
