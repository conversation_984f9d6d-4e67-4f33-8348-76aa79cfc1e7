package com.icarus.bi.service;

import com.icarus.bi.entity.BiCotRule;
import com.icarus.bi.dto.BiCotRuleQueryDTO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * <p>
 * 抽取规则配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
public interface IBiCotRuleService extends IService<BiCotRule> {

    /**
     * 新增抽取规则
     * @param biCotRule 抽取规则
     * @return 是否成功
     */
    boolean saveRule(BiCotRule biCotRule);

    /**
     * 修改抽取规则
     * @param biCotRule 抽取规则
     * @return 是否成功
     */
    boolean updateRule(BiCotRule biCotRule);

    /**
     * 根据ID获取抽取规则详情
     * @param id 主键ID
     * @return 抽取规则
     */
    BiCotRule getRuleById(String id);

    /**
     * 分页查询抽取规则（新方法，支持时间范围和排序）
     * @param queryDTO 查询参数DTO
     * @return 分页结果
     */
    Page<BiCotRule> pageQueryRule(BiCotRuleQueryDTO queryDTO);

    /**
     * 启用抽取规则
     * @param id 主键ID
     * @return 是否成功
     */
    boolean activeRule(String id);

    /**
     * 禁用抽取规则
     * @param id 主键ID
     * @return 是否成功
     */
    boolean disableRule(String id);

    /**
     * 根据规则Code查询抽取规则
     * @param code 规则Code
     * @return 抽取规则
     */
    BiCotRule getRuleByCode(String code);

    /**
     * 根据画布ID查询规则列表
     * @param canvasId 画布ID
     * @param currentPage 当前页
     * @param pageSize 页大小
     * @return 分页结果
     */
    Page<BiCotRule> getRulesByCanvasId(String canvasId, int currentPage, int pageSize);

    /**
     * 删除抽取规则
     * @param id 主键ID
     * @return 是否成功
     */
    boolean deleteRule(String id);

    /**
     * 根据启用状态查询规则列表
     * @param ifActive 是否启用
     * @param currentPage 当前页
     * @param pageSize 页大小
     * @return 分页结果
     */
    Page<BiCotRule> getRulesByActiveStatus(Boolean ifActive, int currentPage, int pageSize);

    /**
     * 更新画布版本号
     * @param id 主键ID
     * @param canvasVersion 画布版本号
     * @return 是否成功
     */
    boolean updateCanvasVersion(String id, String canvasVersion);

    /**
     * 根据画布版本号查询规则列表
     * @param canvasVersion 画布版本号
     * @param currentPage 当前页
     * @param pageSize 页大小
     * @return 分页结果
     */
    Page<BiCotRule> getRulesByCanvasVersion(String canvasVersion, int currentPage, int pageSize);

    /**
     * 获取当前启用的规则
     * @return
     */
    BiCotRule getActiveRule();
}
