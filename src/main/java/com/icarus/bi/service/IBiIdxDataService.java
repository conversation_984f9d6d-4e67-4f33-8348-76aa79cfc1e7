package com.icarus.bi.service;

import com.icarus.bi.entity.BiCotResult;
import com.icarus.bi.dto.QueryCriteria;
import com.icarus.bi.entity.BiIdxData;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.bi.vo.BiIdxDataSetVo;
import com.icarus.bi.vo.BiIdxDataVo;

import java.util.List;

/**
 * <p>
 * 指标数据扩展表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
public interface IBiIdxDataService extends IService<BiIdxData> {

    /**
     * 根据指定的指标配置+文件,获取所有指标值
     */
    List<BiIdxDataSetVo> queryCriteria(QueryCriteria input);

    /**
     * 新增指标数据
     */
    boolean saveIdxData(BiIdxData biIdxData);

    /**
     * 修改指标数据
     */
    boolean updateIdxData(BiIdxData biIdxData);

    /**
     * 根据ID删除指标数据
     */
    boolean deleteIdxDataById(String id);

    /**
     * 根据ID查询指标数据
     */
    BiIdxData getIdxDataById(String id);

    /**
     * 分页查询指标数据
     */
    Page<BiIdxData> pageQueryIdxData(int currentPage, int pageSize, String idxConfId, String minioKey);
    /**
     * 根据任务ID删除指标数据
     *
     * @param taskId 任务ID
     * @return 是否成功
     */
    boolean deleteByTaskId(String taskId);

    /**
     * 要素数据批量保存到指标数据表
     * @param cotResults
     * @param taskId
     */
    void saveBatch(List<BiCotResult> cotResults, String taskId);

    List<BiIdxData> queryMock(QueryCriteria input);

    /**
     * 根据查询条件查询指标数据
     * @param query
     * @return
     */
    List<BiIdxData> selectByQurey(BiIdxData  query);


}
