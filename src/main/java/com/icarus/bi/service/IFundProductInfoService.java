package com.icarus.bi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.bi.entity.FundProductInfo;

import java.util.List;

/**
 * <p>
 * 基金产品信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
public interface IFundProductInfoService extends IService<FundProductInfo> {

    /**
     * 根据任务ID查询基金产品信息列表
     *
     * @param taskId 任务ID
     * @return 基金产品信息列表
     */
    List<FundProductInfo> getByTaskId(String taskId);

    /**
     * 根据产品名称模糊查询
     *
     * @param productName 产品名称
     * @return 基金产品信息列表
     */
    List<FundProductInfo> getByProductNameLike(String productName);

    /**
     * 根据产品类型查询
     *
     * @param productType 产品类型
     * @return 基金产品信息列表
     */
    List<FundProductInfo> getByProductType(String productType);

    /**
     * 保存基金产品信息
     *
     * @param fundProductInfo 基金产品信息
     * @return 是否保存成功
     */
    boolean saveFundProductInfo(FundProductInfo fundProductInfo);

    /**
     * 批量保存基金产品信息
     *
     * @param fundProductInfoList 基金产品信息列表
     * @return 是否保存成功
     */
    boolean batchSaveFundProductInfo(List<FundProductInfo> fundProductInfoList);

    /**
     * 根据任务ID删除基金产品信息
     *
     * @param taskId 任务ID
     * @return 是否删除成功
     */
    boolean deleteByTaskId(String taskId);

    /**
     * 更新基金产品信息
     *
     * @param fundProductInfo 基金产品信息
     * @return 是否更新成功
     */
    boolean updateFundProductInfo(FundProductInfo fundProductInfo);

    /**
     * 根据ID查询基金产品信息
     *
     * @param id 主键ID
     * @return 基金产品信息
     */
    FundProductInfo getById(Long id);

    /**
     * 检查任务ID是否存在基金产品信息
     *
     * @param taskId 任务ID
     * @return 是否存在
     */
    boolean existsByTaskId(String taskId);

    /**
     * 统计任务ID对应的基金产品信息数量
     *
     * @param taskId 任务ID
     * @return 数量
     */
    long countByTaskId(String taskId);
}
