package com.icarus.bi.service;

import com.icarus.bi.entity.BiCotResult;
import com.icarus.bi.dto.BiCotResultQueryDTO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 抽取结果表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
public interface IBiCotResultService extends IService<BiCotResult> {

    /**
     * 批量保存抽取结果
     *
     * @param taskId 任务ID
     * @param extractElements 抽取的要素Map
     * @return 是否成功
     */
    List<BiCotResult> batchSaveResults(String taskId, Map<String, Object> extractElements);

    /**
     * 根据任务ID查询抽取结果
     *
     * @param taskId 任务ID
     * @return 抽取结果列表
     */
    List<BiCotResult> getResultsByTaskId(String taskId, String keyWord);

    /**
     * 根据任务ID和多个条件查询抽取结果（不分页）
     *
     * @param taskId 任务ID（必填）
     * @param elementField 要素字段code（可选，模糊查询）
     * @param elementName 要素字段Name（可选，模糊查询）
     * @param elementResult 要素提取结果（可选，模糊查询）
     * @param elementScopeExp 要素提取章节表达式（可选，模糊查询）
     * @return 抽取结果列表
     */
    List<BiCotResult> getResultsByTaskIdWithConditions(String taskId,
                                                       String elementField,
                                                       String elementName,
                                                       String elementResult,
                                                       String elementScopeExp);

    /**
     * 分页查询抽取结果
     *
     * @param queryDTO 查询参数DTO
     * @return 分页结果
     */
    Page<BiCotResult> pageQueryResults(BiCotResultQueryDTO queryDTO);

    /**
     * 删除任务相关的抽取结果
     *
     * @param taskId 任务ID
     * @return 是否成功
     */
    boolean deleteResultsByTaskId(String taskId);
}
