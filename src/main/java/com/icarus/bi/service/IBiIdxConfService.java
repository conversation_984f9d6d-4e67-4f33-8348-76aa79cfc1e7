package com.icarus.bi.service;

import com.icarus.bi.entity.BiIdxConf;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

import java.util.List;

/**
 * <p>
 * 指标开发表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
public interface IBiIdxConfService extends IService<BiIdxConf> {

    /**
     * 根据场景筛选所有指标配置信息
     */
    List<BiIdxConf> queryByScene(String req);

    /**
     * 新增指标配置
     */
    boolean saveIdxConf(BiIdxConf biIdxConf);

    /**
     * 修改指标配置
     */
    boolean updateIdxConf(BiIdxConf biIdxConf);

    /**
     * 根据ID删除指标配置
     */
    boolean deleteIdxConfById(String id);

    /**
     * 根据ID查询指标配置
     */
    BiIdxConf getIdxConfById(String id);

    /**
     * 分页查询指标配置
     */
    Page<BiIdxConf> pageQueryIdxConf(int currentPage, int pageSize, String code, String name);
    /**
     * 根据cotId查询启用的指标配置
     *
     * @param cotId COT处理器ID
     * @return 指标配置列表
     */
    List<BiIdxConf> getActiveByRuleId(String cotId);
}
