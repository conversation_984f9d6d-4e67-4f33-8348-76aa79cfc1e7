package com.icarus.bi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.icarus.bi.entity.BiIdxObj;

import java.util.List;

/**
 * <p>
 * 数据源配置表（预留暂时不用） 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-16
 */
public interface IBiIdxObjService extends IService<BiIdxObj> {

    BiIdxObj getByName(String sourceDataName);

    List<BiIdxObj> getListByName(String productName);

    /**
     * 搜索, 按名称搜索
     * @param keyword
     * @param limit
     * @return
     */
    List<BiIdxObj> query(String keyword, Integer limit);
}
