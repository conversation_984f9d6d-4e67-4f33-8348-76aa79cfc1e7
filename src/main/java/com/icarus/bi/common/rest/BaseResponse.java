package com.icarus.bi.common.rest;

import java.io.Serializable;

public class BaseResponse<T> implements Serializable {

    private static final long serialVersionUID = -280507252533470147L;

    private int code;
    private String statusText;
    private T data;

    public static <T> BaseResponse<T> ok() {
        return new BaseResponse<>(200, "success", null);
    }

    public static <T> BaseResponse<T> ok(T data) {
        return new BaseResponse<>(200, "success", data);
    }

    public static <T > BaseResponse<T> ok(String message, T data) {
        return new BaseResponse<>(200, message, data);
    }

    public static <T> BaseResponse<T> badRequest(String message, T data) {
        return new BaseResponse<>(400, message, data);
    }

    public static <T> BaseResponse<T> badRequest(String message) {
        return new BaseResponse<>(400, message, null);
    }
    public static <T> BaseResponse<T> serviceError(String message) {
        return new BaseResponse<>(500, message);
    }

	public static <T> BaseResponse<T> serviceError(String message, T data) {
		return new BaseResponse<>(500, message, data);
	}

    public static <T> BaseResponse<T> serviceFail(Throwable e, T data) {
        return new BaseResponse<>(500, e.getMessage(), data);
    }
    public static <T> BaseResponse<T> forbiddenRequest(Throwable e, T data) {
        return new BaseResponse<>(403, e.getMessage(), data);
    }
    public BaseResponse(int code, String message) {
        this.code = code;
        this.statusText = message;
    }

    public BaseResponse(int code, String message, T data) {
        this.code = code;
        this.statusText = message;
        this.data = data;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getStatusText() {
        return statusText;
    }

    public void setStatusText(String message) {
        this.statusText = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
