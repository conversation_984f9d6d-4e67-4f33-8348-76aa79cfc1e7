package com.icarus.bi.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.bi.entity.BiCotTask;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * COT任务分页视图对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-17
 */
@Data
@ApiModel(value = "BiCotTaskPageVO", description = "COT任务分页视图对象")
public class BiCotTaskPageVO {

    /**
     * 当前页码
     */
    @ApiModelProperty("当前页码")
    private long current;

    /**
     * 每页大小
     */
    @ApiModelProperty("每页大小")
    private long size;

    /**
     * 总记录数
     */
    @ApiModelProperty("总记录数")
    private long total;

    /**
     * 总页数
     */
    @ApiModelProperty("总页数")
    private long pages;

    /**
     * 任务列表
     */
    @ApiModelProperty("任务列表")
    private List<BiCotTaskVO> records;

    /**
     * 是否有上一页
     */
    @ApiModelProperty("是否有上一页")
    private boolean hasPrevious;

    /**
     * 是否有下一页
     */
    @ApiModelProperty("是否有下一页")
    private boolean hasNext;

    /**
     * 从MyBatis-Plus的Page对象转换为VO
     *
     * @param page MyBatis-Plus分页对象
     * @return BiCotTaskPageVO
     */
    public static BiCotTaskPageVO fromPage(Page<BiCotTask> page) {
        if (page == null) {
            return null;
        }

        BiCotTaskPageVO pageVO = new BiCotTaskPageVO();
        pageVO.setCurrent(page.getCurrent());
        pageVO.setSize(page.getSize());
        pageVO.setTotal(page.getTotal());
        pageVO.setPages(page.getPages());
        pageVO.setHasPrevious(page.hasPrevious());
        pageVO.setHasNext(page.hasNext());

        // 转换记录列表
        if (page.getRecords() != null) {
            List<BiCotTaskVO> voRecords = page.getRecords().stream()
                    .map(BiCotTaskVO::fromEntity)
                    .collect(Collectors.toList());
            pageVO.setRecords(voRecords);
        }

        return pageVO;
    }

    /**
     * 获取分页统计信息
     *
     * @return 统计信息
     */
    public String getPageInfo() {
        return String.format("第%d页，共%d页，总计%d条记录", current, pages, total);
    }

    /**
     * 判断是否为空页
     *
     * @return 是否为空
     */
    public boolean isEmpty() {
        return records == null || records.isEmpty();
    }

    /**
     * 获取当前页记录数
     *
     * @return 当前页记录数
     */
    public int getCurrentPageSize() {
        return records != null ? records.size() : 0;
    }
}
