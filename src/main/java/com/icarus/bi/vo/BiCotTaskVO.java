package com.icarus.bi.vo;

import com.icarus.bi.dto.BiCotTaskCmttPushReq;
import com.icarus.bi.entity.BiCotTask;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import cn.hutool.json.JSONUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.collection.CollUtil;

import java.time.LocalDateTime;

/**
 * <p>
 * COT任务视图对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-17
 */
@Data
@Slf4j
@ApiModel(value = "BiCotTaskVO", description = "COT任务视图对象")
public class BiCotTaskVO {

    /**
     * ID
     */
    @ApiModelProperty("ID")
    private String id;

    /**
     * 抽取规则ID
     */
    @ApiModelProperty("抽取规则ID")
    private String ruleId;

    /**
     * 抽取规则名称
     */
    @ApiModelProperty("抽取规则名称")
    private String ruleName;

    /**
     * 画布ID
     */
    @ApiModelProperty("画布ID")
    private String canvasId;

    /**
     * 预留字段: 审核状态(UNDO - 待审核, AUDITED - 已审核)
     */
    @ApiModelProperty("预留字段: 审核状态(UNDO - 待审核, AUDITED - 已审核)")
    private String auditStatus;

    /**
     * 执行状态(TODO - 待启动, COT_DOING - COT进行中, COT_DONE - COT已完成, IDX_DOING - IDX进行中, IDX_DONE - IDX已完成, ERROR - 异常)
     */
    @ApiModelProperty("执行状态(TODO - 待启动, COT_DOING - COT进行中, COT_DONE - COT已完成, IDX_DOING - IDX进行中, IDX_DONE - IDX已完成, ERROR - 异常)")
    private String taskStatus;

    /**
     * 任务开始时间
     */
    @ApiModelProperty("任务开始时间")
    private LocalDateTime taskStartTime;

    /**
     * 任务结束时间
     */
    @ApiModelProperty("任务结束时间")
    private LocalDateTime taskEndTime;

    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String extraAttrs;

    /**
     * 要素提取源文件key
     */
    @ApiModelProperty("要素提取源文件key")
    private String sourceFileKey;

    /**
     * 文件URL（从extraAttrs中提取）
     */
    @ApiModelProperty("文件URL")
    private String fileUrl;

    /**
     * 文件名称（从extraAttrs中提取）
     */
    @ApiModelProperty("文件名称")
    private String fileName;

    /**
     * 文件类型（从extraAttrs中提取）
     */
    @ApiModelProperty("文件类型")
    private String fileType;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    /**
     * 从BiCotTask实体转换为VO
     *
     * @param task BiCotTask实体
     * @return BiCotTaskVO
     */
    public static BiCotTaskVO fromEntity(BiCotTask task) {
        if (task == null) {
            return null;
        }

        BiCotTaskVO vo = new BiCotTaskVO();
        vo.setId(task.getId());
        vo.setRuleId(task.getRuleId());
        vo.setRuleName(task.getRuleName());
        vo.setCanvasId(task.getCanvasId());
        vo.setAuditStatus(task.getAuditStatus());
        vo.setTaskStatus(task.getTaskStatus());
        vo.setTaskStartTime(task.getTaskStartTime());
        vo.setTaskEndTime(task.getTaskEndTime());
        vo.setExtraAttrs(task.getExtraAttrs());
        vo.setSourceFileKey(task.getSourceFileKey());
        vo.setCreateTime(task.getCreateTime());
        vo.setUpdateTime(task.getUpdateTime());
        vo.setFileUrl(task.getSourceFileKey());

        // SourceFileKey 提取文件信息
        if (StrUtil.isNotBlank(task.getSourceFileKey())){
            String sourceFileKey = task.getSourceFileKey();
            //sourceFileKey是crawler/517/2a31f9a3/富国安丰60天持有期债券型发起式证券投资基金招募说明书2025-07-30_下载文件.pdf格式，从这里提取文件名和文件类型
            String fileName = sourceFileKey.substring(sourceFileKey.lastIndexOf("/") + 1);
            String fileType = sourceFileKey.substring(sourceFileKey.lastIndexOf(".") + 1);
            vo.setFileType(fileType);
            vo.setFileName(fileName);
        }

        return vo;
    }


    /**
     * 从extraAttrs JSON中提取文件信息
     *
     * @param extraAttrs 扩展属性JSON字符串
     * @return 语料库名称
     */
    private static BiCotTaskCmttPushReq.FilesDTO extractFileDTO(String extraAttrs) {
        try {
            if (StrUtil.isBlank(extraAttrs)) {
                log.debug("[提取语料库文件信息]extraAttrs为空，返回null");
                return null;
            }

            log.debug("[提取语料库文件信息]开始解析extraAttrs, length:{}", extraAttrs.length());

            BiCotTaskCmttPushReq cmttReq = JSONUtil.toBean(extraAttrs, BiCotTaskCmttPushReq.class, true);

            // 校验cmttReq是否为null
            if (ObjectUtil.isNull(cmttReq)) {
                log.warn("[提取语料库文件信息]JSON解析后cmttReq为null, extraAttrs:{}", extraAttrs);
                return null;
            }

            // 校验files是否为null或空
            if (CollUtil.isEmpty(cmttReq.getFiles())) {
                log.warn("[提取语料库文件信息]files为空, extraAttrs:{}", extraAttrs);
                return null;
            }

            return  cmttReq.getFiles().get(0);
        } catch (Exception e) {
            // JSON解析失败时返回null
            log.error("[提取语料库文件信息]解析失败, extraAttrs:{}, error:{}", extraAttrs, e.getMessage(), e);
            return null;
        }
    }


}
