package com.icarus.bi.vo;

import com.icarus.bi.entity.BiIdxData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <p>
 * 指标数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-16
 */
@Getter
@Setter
@ToString
@ApiModel(value = "BiIdxDataVo", description = "BiIdxDataVo")
public class BiIdxDataVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty("指标配置ID")
    private String idxConfId;

    @ApiModelProperty("指标code")
    private String idxConfCode;

    @ApiModelProperty("指标name")
    private String idxConfName;

    @ApiModelProperty("产品ID")
    private String productId;

    @ApiModelProperty("值")
    private String value;

    @ApiModelProperty("产品名称")
    private String productName;

    public static BiIdxDataVo convert(BiIdxData data) {
        BiIdxDataVo vo = new BiIdxDataVo();
        vo.setId(data.getId());
        vo.setIdxConfId(data.getIdxConfId());
        vo.setIdxConfCode(data.getIdxConfCode());
        vo.setIdxConfName(data.getIdxConfName());
        vo.setValue(data.getValue());
        vo.setProductId(data.getObjId());
        vo.setProductName(data.getObjName());
        return vo;
    }
}
