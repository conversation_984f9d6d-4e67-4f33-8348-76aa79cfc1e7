package com.icarus.bi.vo;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 指标数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-16
 */
@Getter
@Setter
@ToString
@ApiModel(value = "BiIdxDataSetVo", description = "BiIdxDataSetVo")
public class BiIdxDataSetVo implements Serializable {

    private static final long serialVersionUID = 1L;
    // 产品名称
    String productName;
    List<BiIdxDataVo> data;
}
