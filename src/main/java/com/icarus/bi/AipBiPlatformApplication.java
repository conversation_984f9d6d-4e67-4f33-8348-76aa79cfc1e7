package com.icarus.bi;


import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.retry.annotation.EnableRetry;

@SpringBootApplication(scanBasePackages = {"com.icarus"})
@MapperScan("com.icarus.bi.mapper")
@EnableRetry
public class AipBiPlatformApplication {
    public static void main(String[] args) {
        SpringApplication.run(AipBiPlatformApplication.class, args);
    }
}