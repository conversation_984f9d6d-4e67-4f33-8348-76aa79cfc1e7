package com.icarus.bi.util;

import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

public class SetUtil {
    /**
     * 计算多个集合的交集
     */
    @SafeVarargs
    public static <T> Set<T> intersectAll(Set<T>... sets) {
        if (sets == null || sets.length == 0) {
            return null;
        }
        Set<T> result = null;
        for (Set<T> set : sets) {
            if (set == null) {
                continue;
            }
            if (result == null) {
                result = new HashSet<>(set);
                continue;
            }
            result.retainAll(set);
        }
        return result;
    }

    /**
     * 计算多个集合的并集
     */
    @SafeVarargs
    public static <T> Set<T> unionAll(Set<T>... sets) {
        if (sets == null || sets.length == 0) {
            return Collections.emptySet();
        }
        Set<T> result = new HashSet<>();
        for (Collection<T> set : sets) {
            if (set == null) {
                continue;
            }
            result.addAll(set);
        }
        return result;
    }
}    