package com.icarus.bi.util;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.icarus.bi.dto.QueryCondition;
import com.icarus.bi.dto.QueryCriteria;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * 动态查询构建器
 * 根据查询条件动态构建MyBatis-Plus的Lambda查询表达式
 */
@Slf4j
@Component
public class DynamicQueryBuilder {

    /**
     * 根据查询条件构建查询包装器
     *
     * @param queryCriteria 查询条件
     * @param entityClass   实体类
     * @param <T>          实体类型
     * @return 查询包装器
     */
    public <T> QueryWrapper<T> buildQueryWrapper(QueryCriteria queryCriteria, Class<T> entityClass) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        
        if (queryCriteria == null) {
            log.warn("查询条件为空，返回空的查询包装器");
            return queryWrapper;
        }
        
        // 处理目标字段（select字段）
        if (queryCriteria.getTargets() != null && !queryCriteria.getTargets().isEmpty()) {
            // 将targets中的字段转换为数据库字段名
            String[] selectFields = queryCriteria.getTargets().stream()
                    .map(this::convertTargetField)
                    .toArray(String[]::new);
            queryWrapper.select(selectFields);
            log.info("设置查询字段：{}", Arrays.toString(selectFields));
        } else {
            log.info("未指定目标字段，将查询所有字段");
        }
        
        // 处理查询条件
        if (queryCriteria.getConditions() == null || queryCriteria.getConditions().isEmpty()) {
            log.warn("查询条件为空，仅返回带select字段的查询包装器");
            return queryWrapper;
        }
        
        List<QueryCondition> conditions = queryCriteria.getConditions();
        log.info("开始构建动态查询，条件数量：{}", conditions.size());
        
        for (int i = 0; i < conditions.size(); i++) {
            QueryCondition condition = conditions.get(i);
            
            if (!StringUtils.hasText(condition.getField()) || !StringUtils.hasText(condition.getOperator())) {
                log.warn("跳过无效条件：字段或操作符为空");
                continue;
            }
            if ("productName".equals(condition.getField())) {
                log.warn("productName字段不参与where条件中。");
                continue;
            }
            
            // 根据逻辑连接符决定是AND还是OR
            boolean isOr = "OR".equalsIgnoreCase(condition.getLogic());
            
            // 根据操作符构建查询条件（使用字段名而不是Lambda函数）
            buildConditionByFieldName(queryWrapper, condition, isOr);
            
            log.info("添加查询条件：字段={}, 操作符={}, 值={}, 逻辑={}", 
                    condition.getField(), condition.getOperator(), condition.getValue(), condition.getLogic());
        }
        
        return queryWrapper;
    }
    
    /**
     * 根据操作符构建具体的查询条件（使用字段名）
     */
    private <T> void buildConditionByFieldName(QueryWrapper<T> queryWrapper, QueryCondition condition, boolean isOr) {
        String operator = condition.getOperator().toUpperCase();
        String fieldName = getDbFieldName(condition.getField());
        String value = condition.getValue();
        
        switch (operator) {
            case "=":
            case "EQ":
                if (isOr) {
                    queryWrapper.or().eq(fieldName, value);
                } else {
                    queryWrapper.eq(fieldName, value);
                }
                break;
                
            case "!=":
            case "NE":
                if (isOr) {
                    queryWrapper.or().ne(fieldName, value);
                } else {
                    queryWrapper.ne(fieldName, value);
                }
                break;
                
            case "LIKE":
                if (isOr) {
                    queryWrapper.or().like(fieldName, value);
                } else {
                    queryWrapper.like(fieldName, value);
                }
                break;
                
            case "NOT LIKE":
                if (isOr) {
                    queryWrapper.or().notLike(fieldName, value);
                } else {
                    queryWrapper.notLike(fieldName, value);
                }
                break;
                
            case ">":
            case "GT":
                if (isOr) {
                    queryWrapper.or().gt(fieldName, value);
                } else {
                    queryWrapper.gt(fieldName, value);
                }
                break;
                
            case ">=":
            case "GE":
                if (isOr) {
                    queryWrapper.or().ge(fieldName, value);
                } else {
                    queryWrapper.ge(fieldName, value);
                }
                break;
                
            case "<":
            case "LT":
                if (isOr) {
                    queryWrapper.or().lt(fieldName, value);
                } else {
                    queryWrapper.lt(fieldName, value);
                }
                break;
                
            case "<=":
            case "LE":
                if (isOr) {
                    queryWrapper.or().le(fieldName, value);
                } else {
                    queryWrapper.le(fieldName, value);
                }
                break;
                
            case "IN":
                List<String> inValues = Arrays.asList(value.split(","));
                if (isOr) {
                    queryWrapper.or().in(fieldName, inValues);
                } else {
                    queryWrapper.in(fieldName, inValues);
                }
                break;
                
            case "NOT IN":
                List<String> notInValues = Arrays.asList(value.split(","));
                if (isOr) {
                    queryWrapper.or().notIn(fieldName, notInValues);
                } else {
                    queryWrapper.notIn(fieldName, notInValues);
                }
                break;
                
            case "IS NULL":
                if (isOr) {
                    queryWrapper.or().isNull(fieldName);
                } else {
                    queryWrapper.isNull(fieldName);
                }
                break;
                
            case "IS NOT NULL":
                if (isOr) {
                    queryWrapper.or().isNotNull(fieldName);
                } else {
                    queryWrapper.isNotNull(fieldName);
                }
                break;
                
            default:
                log.warn("不支持的操作符：{}", operator);
                break;
        }
    }
    
    /**
     * 转换目标字段（用于select语句）
     * 处理聚合函数和普通字段名
     */
    private String convertTargetField(String targetField) {
        if (!StringUtils.hasText(targetField)) {
            return targetField;
        }
        
        // 如果包含聚合函数（如COUNT(*)AS数量），直接返回
        if (targetField.toUpperCase().contains("COUNT(") || 
            targetField.toUpperCase().contains("SUM(") || 
            targetField.toUpperCase().contains("AVG(") || 
            targetField.toUpperCase().contains("MAX(") || 
            targetField.toUpperCase().contains("MIN(") ||
            targetField.contains("AS") || targetField.contains("as")) {
            return targetField;
        }
        
        // 普通字段名转换为数据库字段名
        return getDbFieldName(targetField);
    }
    
    /**
     * 获取数据库字段名
     * 将Java字段名转换为数据库字段名（驼峰转下划线）
     */
    private String getDbFieldName(String fieldName) {
        if (!StringUtils.hasText(fieldName)) {
            return fieldName;
        }
        
        // 如果已经是下划线格式，直接返回
        if (fieldName.contains("_") || fieldName.contains("-")) {
            return fieldName;
        }
        
        // 驼峰转下划线
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < fieldName.length(); i++) {
            char c = fieldName.charAt(i);
            if (Character.isUpperCase(c)) {
                if (i > 0) {
                    result.append("_");
                }
                result.append(Character.toLowerCase(c));
            } else {
                result.append(c);
            }
        }
        
        return result.toString();
    }
    

}