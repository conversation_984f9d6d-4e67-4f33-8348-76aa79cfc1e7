package com.icarus.bi.util;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.icarus.bi.config.AppConfigs;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * AIP-Platform 接口调用工具类
 * 1. 默认读app-configs下的url配置，
 * 2. 以下path参数入参都从“子系统”开始往后补全： 例如： /ai/search/aiSearch
 */
public class AipApiClientUtil {

    private static final Logger logger = LoggerFactory.getLogger(AipApiClientUtil.class);
    public static final int TIMEOUT = 50000;

    public static String callApi(String path, Object body) {
        try {
            String url = AppConfigs.aip().getSysReqUrl() + path;
            String requestBody = JSONUtil.toJsonStr(body);
            logger.info("Calling API: {} with request body: {}", url, requestBody);

            String response = HttpUtil.post(url, requestBody, TIMEOUT);

            logger.info("API response: {}", response);
            return response;
        } catch (Exception e) {
            logger.error("Error occurred while calling API", e);
            throw e;
        }
    }

    public static String callApi(String path, Map<String, String> queryParam, Object body) {
        try {
            if (queryParam == null) {
                return callApi(path, body);
            } else {
                if (!queryParam.isEmpty()) {
                    StringBuffer queryParamStr = new StringBuffer(path + "?");
                    queryParam.forEach((k, v) -> queryParamStr.append(k).append("=").append(v));
                    path = queryParamStr.toString();
                }
            }

            String url = AppConfigs.aip().getSysReqUrl() + path;
            String requestBody = JSONUtil.toJsonStr(body);
            logger.info("Calling API with query parameters: {} with request body: {}", url, requestBody);

            String response = HttpUtil.post(url, requestBody, TIMEOUT);

            logger.info("API response: {}", response);
            return response;
        } catch (Exception e) {
            logger.error("Error occurred while calling API with query parameters", e);
            throw e;
        }
    }

    /**
     * 解析AIP返回结果
     *
     * @param result
     * @return
     */
    public static Object parseAipResult(String result){
        JSONObject root = new JSONObject(result);

        // 检查第一层data
        if (!root.containsKey("data")) {
            return null;
        }

        JSONObject firstLevelData = root.getJSONObject("data");

        // 检查result是否存在且是字符串
        if (!firstLevelData.containsKey("res")) {
            return null;
        }

        // 解析result字符串为JSON
        return firstLevelData.get("res");
    }

    /**
     * 将Object转换为指定类型的对象列表
     *
     * @param entries 原始对象数据
     * @param clazz 目标对象类型
     * @param <T> 泛型参数
     * @return 转换后的对象列表
     */
    public static  <T> List<T> convertEntriesToList(Object entries, Class<T> clazz) {
        List<T> result = new ArrayList<>();

        if (entries != null) {
            // 将Object转换为JSONArray
            JSONArray jsonArray = JSONUtil.parseArray(entries);

            // 循环转换为指定类型的对象
            for (int i = 0; i < jsonArray.size(); i++) {
                T item = jsonArray.get(i, clazz);
                result.add(item);
            }
        }

        return result;
    }
}
