package com.icarus.bi.util;


import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;

import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-23
 */
public class MybatisPlusGenerator {
	public static void main(String[] args) {
		String url = "**************************************************";
		String username = "pgvector";
		String password = "pgvector";

		FastAutoGenerator
				.create(url, username, password)
				.globalConfig(builder -> builder.author("yss-ai")
						.outputDir(Paths.get("src").toAbsolutePath() + "/main/java")
						.enableSwagger())
				.packageConfig(builder -> builder.parent("com.icarus.bi"))
				.strategyConfig(builder -> {
					// 在方法中用,间隔要生成的表名,全部生成则"all"
					builder.addInclude(getTables("bi_cot_result," +
									"bi_cot_rule," +
									"bi_cot_task," +
									"bi_idx_conf," +
									"bi_idx_obj," +
									"bi_idx_data"))
							.entityBuilder()
							.enableLombok()
							.enableFileOverride() // 启用覆盖模式(重新生成一存在实体的时候启用)
					;
				})
				// 使用Freemarker引擎模板，默认的是Velocity引擎模板
				.templateEngine(new FreemarkerTemplateEngine())
				.execute();
	}

	// 处理 all 情况
	protected static List<String> getTables(String tables) {
		return "all".equals(tables) ? Collections.emptyList() : Arrays.asList(tables.split(","));
	}
}
