package com.icarus.bi.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 爬虫请求限流器
 * 控制对下游爬虫系统的请求频率，避免压垮下游服务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CrawlRateLimiter {
    
    /**
     * 最大并发请求数（信号量控制）
     */
    @Value("${crawl.rate-limit.max-concurrent:3}")
    private int maxConcurrent;
    
    /**
     * 请求间隔时间（毫秒）
     */
    @Value("${crawl.rate-limit.interval-ms:2000}")
    private long intervalMs;
    
    /**
     * 每分钟最大请求数
     */
    @Value("${crawl.rate-limit.max-per-minute:20}")
    private int maxPerMinute;
    
    private Semaphore concurrentSemaphore;
    private final AtomicLong lastRequestTime = new AtomicLong(0);
    private final AtomicLong requestCount = new AtomicLong(0);
    private final AtomicLong windowStartTime = new AtomicLong(System.currentTimeMillis());
    
    /**
     * 初始化限流器
     */
    public void init() {
        if (concurrentSemaphore == null) {
            concurrentSemaphore = new Semaphore(maxConcurrent);
            log.info("爬虫限流器初始化完成 - 最大并发: {}, 请求间隔: {}ms, 每分钟最大: {}", 
                    maxConcurrent, intervalMs, maxPerMinute);
        }
    }
    
    /**
     * 获取许可证，控制并发和频率
     * @param timeoutSeconds 超时时间（秒）
     * @return 是否获取成功
     */
    public boolean tryAcquire(int timeoutSeconds) {
        init();
        
        try {
            // 1. 控制并发数
            if (!concurrentSemaphore.tryAcquire(timeoutSeconds, TimeUnit.SECONDS)) {
                log.warn("获取并发许可证超时，当前可用: {}", concurrentSemaphore.availablePermits());
                return false;
            }
            
            // 2. 控制请求间隔
            long currentTime = System.currentTimeMillis();
            long lastTime = lastRequestTime.get();
            long timeSinceLastRequest = currentTime - lastTime;
            
            if (timeSinceLastRequest < intervalMs) {
                long sleepTime = intervalMs - timeSinceLastRequest;
                log.debug("请求间隔控制，等待: {}ms", sleepTime);
                try {
                    Thread.sleep(sleepTime);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    concurrentSemaphore.release();
                    return false;
                }
            }
            
            // 3. 控制每分钟请求数
            if (!checkRateLimit()) {
                concurrentSemaphore.release();
                return false;
            }
            
            lastRequestTime.set(System.currentTimeMillis());
            return true;
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("获取限流许可证被中断", e);
            return false;
        }
    }
    
    /**
     * 释放许可证
     */
    public void release() {
        if (concurrentSemaphore != null) {
            concurrentSemaphore.release();
        }
    }
    
    /**
     * 检查频率限制
     */
    private boolean checkRateLimit() {
        long currentTime = System.currentTimeMillis();
        long windowStart = windowStartTime.get();
        
        // 如果时间窗口超过1分钟，重置计数器
        if (currentTime - windowStart > 60000) {
            if (windowStartTime.compareAndSet(windowStart, currentTime)) {
                requestCount.set(0);
            }
        }
        
        long count = requestCount.incrementAndGet();
        if (count > maxPerMinute) {
            log.warn("达到每分钟请求限制: {}/{}", count, maxPerMinute);
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取当前限流状态
     */
    public String getStatus() {
        init();
        long currentTime = System.currentTimeMillis();
        long windowStart = windowStartTime.get();
        long timeInWindow = currentTime - windowStart;
        
        return String.format(
            "限流状态 - 可用并发: %d/%d, 当前窗口请求数: %d/%d, 窗口剩余时间: %ds",
            concurrentSemaphore.availablePermits(), maxConcurrent,
            requestCount.get(), maxPerMinute,
            Math.max(0, (60000 - timeInWindow) / 1000)
        );
    }
}
