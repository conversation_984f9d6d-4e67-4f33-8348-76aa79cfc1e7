package com.icarus.bi.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.icarus.bi.common.rest.BaseResponse;
import com.icarus.bi.entity.BiCotResult;
import com.icarus.bi.dto.BiCotResultQueryDTO;
import com.icarus.bi.service.IBiCotResultService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 抽取结果表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Tag(name = "抽取结果表")
@Slf4j
@RestController
@Validated
@RequestMapping("/bi/cotResult")
public class BiCotResultController {

    @Resource
    private IBiCotResultService biCotResultService;

    /**
     * 根据任务ID查询结果（支持多条件查询）
     *
     * @param taskId 任务ID（必填）
     * @param elementField 要素字段code（可选，模糊查询）
     * @param elementName 要素字段Name（可选，模糊查询）
     * @param elementResult 要素提取结果（可选，模糊查询）
     * @param elementScopeExp 要素提取章节表达式（可选，模糊查询）
     * @return 查询结果列表
     */
    @Operation(summary ="根据任务ID查询结果（支持多条件查询）")
    @GetMapping("/getResultsByTaskId")
    public BaseResponse<List<BiCotResult>> getResultsByTaskId(@RequestParam String taskId,
                                                              @RequestParam(required = false) String elementField,
                                                              @RequestParam(required = false) String elementName,
                                                              @RequestParam(required = false) String elementResult,
                                                              @RequestParam(required = false) String elementScopeExp) {
        try {
            if (StrUtil.isBlank(taskId)) {
                return BaseResponse.badRequest("任务ID不能为空");
            }

            List<BiCotResult> results = biCotResultService.getResultsByTaskIdWithConditions(
                    taskId, elementField, elementName, elementResult, elementScopeExp);

            log.info("[根据任务ID查询结果]成功, taskId:{}, 结果数量:{}", taskId, results.size());

            return BaseResponse.ok(results);

        } catch (Exception e) {
            log.error("[根据任务ID查询结果]失败, taskId:{}, error:{}", taskId, e.getMessage(), e);
            return BaseResponse.serviceError("查询失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询抽取结果
     *
     * @param queryDTO 查询参数DTO
     * @return 分页查询结果
     */
    @Operation(summary ="分页查询抽取结果")
    @PostMapping("/pageQuery")
    public BaseResponse<Page<BiCotResult>> pageQueryResults(@RequestBody @Validated BiCotResultQueryDTO queryDTO) {
        try {
            if (queryDTO == null) {
                return BaseResponse.badRequest("查询参数不能为空");
            }

            Page<BiCotResult> result = biCotResultService.pageQueryResults(queryDTO);

            log.info("[分页查询抽取结果]成功, taskId:{}, 当前页:{}, 页大小:{}, 总记录数:{}",
                    queryDTO.getTaskId(), queryDTO.getCurrentPage(), queryDTO.getPageSize(), result.getTotal());

            return BaseResponse.ok(result);

        } catch (Exception e) {
            log.error("[分页查询抽取结果]失败, queryDTO:{}, error:{}", queryDTO, e.getMessage(), e);
            return BaseResponse.serviceError("查询失败: " + e.getMessage());
        }
    }

    /**
     * 批量保存结果
     * @param taskId
     * @param extractElements
     * @return
     */
    @Operation(summary ="批量保存结果")
    @PostMapping("/saveResults")
    public BaseResponse<List<BiCotResult>> saveResults(String taskId, String extractElements) {
        if (StrUtil.isBlank(taskId) || StrUtil.isBlank(extractElements)) {
            return BaseResponse.badRequest("任务ID和抽取结果不能为空");
        }
        if (!JSONUtil.isTypeJSON(extractElements)){
            return BaseResponse.badRequest("抽取结果不是有效的JSON字符串");
        }
        Map bean = JSONUtil.toBean(extractElements, Map.class);
        return BaseResponse.ok(biCotResultService.batchSaveResults(taskId, bean));
    }
}
