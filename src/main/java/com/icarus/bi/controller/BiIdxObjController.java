package com.icarus.bi.controller;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import com.icarus.bi.common.rest.BaseResponse;
import com.icarus.bi.entity.BiIdxObj;
import com.icarus.bi.service.IBiIdxObjService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 指标描述对象表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-16
 */
@Api(description = "指标描述对象表接口")
@RestController
@RequestMapping("/bi/idxObj")
@Slf4j
public class BiIdxObjController {

    @Autowired
    private IBiIdxObjService biIdxObjService;

    /**
     * 查询
     *
     * @param keyword 关键字
     * @param limit   限制数量
     * @return
     */
    @ApiOperation(value = "根据名称模糊查询")
    @GetMapping("/query")
    public BaseResponse<List<BiIdxObj>> query(@RequestParam String keyword, @RequestParam (required = false) Integer limit) {
        log.info("query keyword: {}, limit: {}", keyword, limit);
        if (StrUtil.isBlank( keyword)){
            return BaseResponse.badRequest("参数错误, keyword不能为空");
        }
        List<BiIdxObj> query = biIdxObjService.query(keyword, limit);
        return BaseResponse.ok(query);
    }


}
