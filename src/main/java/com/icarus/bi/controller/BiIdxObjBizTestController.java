package com.icarus.bi.controller;

import com.icarus.bi.common.rest.BaseResponse;
import com.icarus.bi.entity.BiCotResult;
import com.icarus.bi.entity.BiIdxObj;
import com.icarus.bi.service.IBiCotResultService;
import com.icarus.bi.service.biz.BiIdxDataBizService;
import com.icarus.bi.service.biz.BiIdxObjBizService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * BiIdxObjBizService测试控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-17
 */
@Api(description = "BiIdxObjBizService测试控制器")
@Slf4j
@RestController
@RequestMapping("/biIdxObj/biz/test")
public class BiIdxObjBizTestController {

    @Resource
    private BiIdxObjBizService biIdxObjBizService;

    @Resource
    private IBiCotResultService biCotResultService;

    @Resource
    private BiIdxDataBizService biIdxDataBizService;

    @ApiOperation(value = "测试根据抽取结果创建对象")
    @GetMapping("/testCreateObjFromResult")
    public BaseResponse<Map<String, Object>> testCreateObjFromResult(
            @ApiParam(value = "任务ID", required = true) @RequestParam String taskId,
            @ApiParam(value = "规则ID", required = true) @RequestParam String ruleId) {
        try {
            log.info("[测试根据抽取结果创建对象]开始, taskId:{}, ruleId:{}", taskId, ruleId);
            
            // 获取任务的抽取结果
            List<BiCotResult> extractResults = biCotResultService.getResultsByTaskId(taskId, null);
            
            if (extractResults.isEmpty()) {
                return BaseResponse.badRequest("任务没有抽取结果数据");
            }
            
            long startTime = System.currentTimeMillis();
            BiIdxObj createdObj = biIdxObjBizService.createObjFromResult(taskId, ruleId, extractResults);

            boolean generateIdxSuccess = biIdxDataBizService.generateIdxDataFromResults(createdObj.getId(), ruleId, extractResults);
            long endTime = System.currentTimeMillis();
            
            Map<String, Object> response = new HashMap<>();
            response.put("taskId", taskId);
            response.put("ruleId", ruleId);
            response.put("inputResultsCount", extractResults.size());
            response.put("executionTimeMs", endTime - startTime);
            
            if (createdObj != null) {
                response.put("success", true);
                
                // 分析抽取结果
                response.put("extractResultsAnalysis", analyzeExtractResults(extractResults));
                
            } else {
                response.put("success", false);
                response.put("reason", "未能创建对象，可能原因：规则配置不存在、主键字段未找到、主键值为空");
            }
            
            log.info("[测试根据抽取结果创建对象]完成, taskId:{}, ruleId:{}, 成功:{}", 
                    taskId, ruleId, createdObj != null);
            
            return BaseResponse.ok("测试完成", response);
            
        } catch (Exception e) {
            log.error("[测试根据抽取结果创建对象]失败, taskId:{}, ruleId:{}, error:{}", 
                    taskId, ruleId, e.getMessage(), e);
            return BaseResponse.serviceError("测试失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "测试创建对象（使用模拟数据）")
    @PostMapping("/testCreateObjWithMockData")
    public BaseResponse<Map<String, Object>> testCreateObjWithMockData(
            @ApiParam(value = "规则ID", required = true) @RequestParam String ruleId,
            @ApiParam(value = "对象名称", required = true) @RequestParam String objName,
            @ApiParam(value = "数据类型") @RequestParam(defaultValue = "company") String dataType) {
        try {
            log.info("[测试创建对象-模拟数据]开始, ruleId:{}, objName:{}, dataType:{}", 
                    ruleId, objName, dataType);
            
            String mockTaskId = "mock-task-" + System.currentTimeMillis();
            List<BiCotResult> mockResults = createMockExtractResults(mockTaskId, objName, dataType);
            
            long startTime = System.currentTimeMillis();
            BiIdxObj createdObj = biIdxObjBizService.createObjFromResult(mockTaskId, ruleId, mockResults);
            long endTime = System.currentTimeMillis();
            
            Map<String, Object> response = new HashMap<>();
            response.put("mockTaskId", mockTaskId);
            response.put("ruleId", ruleId);
            response.put("objName", objName);
            response.put("dataType", dataType);
            response.put("mockResultsCount", mockResults.size());
            response.put("executionTimeMs", endTime - startTime);
            response.put("mockResults", mockResults);
            
            if (createdObj != null) {
                response.put("success", true);
                response.put("createdObj", createdObj);
            } else {
                response.put("success", false);
                response.put("reason", "创建失败，请检查规则配置和主键设置");
            }
            
            log.info("[测试创建对象-模拟数据]完成, ruleId:{}, objName:{}, 成功:{}", 
                    ruleId, objName, createdObj != null);
            
            return BaseResponse.ok("模拟数据测试完成", response);
            
        } catch (Exception e) {
            log.error("[测试创建对象-模拟数据]失败, ruleId:{}, objName:{}, error:{}", 
                    ruleId, objName, e.getMessage(), e);
            return BaseResponse.serviceError("测试失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "测试对象更新逻辑")
    @PostMapping("/testObjUpdateLogic")
    public BaseResponse<Map<String, Object>> testObjUpdateLogic(
            @ApiParam(value = "规则ID", required = true) @RequestParam String ruleId,
            @ApiParam(value = "对象名称", required = true) @RequestParam String objName) {
        try {
            log.info("[测试对象更新逻辑]开始, ruleId:{}, objName:{}", ruleId, objName);
            
            Map<String, Object> response = new HashMap<>();
            
            // 第一次创建
            String taskId1 = "test-task-1-" + System.currentTimeMillis();
            List<BiCotResult> results1 = createMockExtractResults(taskId1, objName, "company");
            
            BiIdxObj obj1 = biIdxObjBizService.createObjFromResult(taskId1, ruleId, results1);
            
            // 等待1秒确保时间差异
            Thread.sleep(1000);
            
            // 第二次创建（应该是更新）
            String taskId2 = "test-task-2-" + System.currentTimeMillis();
            List<BiCotResult> results2 = createMockExtractResults(taskId2, objName, "company_updated");
            
            BiIdxObj obj2 = biIdxObjBizService.createObjFromResult(taskId2, ruleId, results2);

            
            // 验证是否是同一个对象
            boolean isSameObject = obj1 != null && obj2 != null && 
                    Objects.equals(obj1.getId(), obj2.getId());
            

            
            log.info("[测试对象更新逻辑]完成, ruleId:{}, objName:{}, 更新逻辑正确:{}", 
                    ruleId, objName, isSameObject);
            
            return BaseResponse.ok("对象更新逻辑测试完成", response);
            
        } catch (Exception e) {
            log.error("[测试对象更新逻辑]失败, ruleId:{}, objName:{}, error:{}", 
                    ruleId, objName, e.getMessage(), e);
            return BaseResponse.serviceError("测试失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "测试边界情况")
    @PostMapping("/testEdgeCases")
    public BaseResponse<Map<String, Object>> testEdgeCases(
            @ApiParam(value = "规则ID", required = true) @RequestParam String ruleId) {
        try {
            log.info("[测试边界情况]开始, ruleId:{}", ruleId);
            
            Map<String, Object> response = new HashMap<>();
            
            // 测试1: 空结果列表
            BiIdxObj obj1 = biIdxObjBizService.createObjFromResult("test-task", ruleId, new ArrayList<>());

            
            // 测试2: null结果列表
            BiIdxObj obj2 = biIdxObjBizService.createObjFromResult("test-task", ruleId, null);

            
            // 测试3: 无主键字段的结果
            List<BiCotResult> nonPrimaryResults = Arrays.asList(
                    createMockResult("test-task", "field1", "value1"),
                    createMockResult("test-task", "field2", "value2")
            );
            BiIdxObj obj3 = biIdxObjBizService.createObjFromResult("test-task", ruleId, nonPrimaryResults);

            
            // 测试4: 空主键值
            List<BiCotResult> emptyPrimaryResults = Arrays.asList(
                    createMockResult("test-task", "companyName", ""),
                    createMockResult("test-task", "field1", "value1")
            );
            BiIdxObj obj4 = biIdxObjBizService.createObjFromResult("test-task", ruleId, emptyPrimaryResults);

            

            
            log.info("[测试边界情况]完成, ruleId:{}", ruleId);
            
            return BaseResponse.ok("边界情况测试完成", response);
            
        } catch (Exception e) {
            log.error("[测试边界情况]失败, ruleId:{}, error:{}", ruleId, e.getMessage(), e);
            return BaseResponse.serviceError("测试失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "生成测试数据示例")
    @GetMapping("/generateTestDataExamples")
    public BaseResponse<Map<String, Object>> generateTestDataExamples() {
        Map<String, Object> examples = new HashMap<>();
        
        examples.put("mockCompanyData", createMockExtractResults("test-task", "测试公司", "company"));
        examples.put("mockPersonData", createMockExtractResults("test-task", "张三", "person"));
        examples.put("mockProductData", createMockExtractResults("test-task", "测试产品", "product"));
        

        
        return BaseResponse.ok("测试数据示例", examples);
    }

    /**
     * 分析抽取结果
     */
    private Map<String, Object> analyzeExtractResults(List<BiCotResult> extractResults) {
        Map<String, Object> analysis = new HashMap<>();
        
        analysis.put("totalFields", extractResults.size());
        analysis.put("fieldNames", extractResults.stream()
                .map(BiCotResult::getElementField)
                .collect(Collectors.toList()));
        analysis.put("fieldValues", extractResults.stream()
                .collect(Collectors.toMap(
                        BiCotResult::getElementField,
                        result -> Optional.ofNullable(result.getElementResult()).orElse(""),
                        (existing, replacement) -> replacement
                )));
        
        long nonEmptyFields = extractResults.stream()
                .mapToLong(result -> Optional.ofNullable(result.getElementResult())
                        .filter(value -> !value.trim().isEmpty())
                        .map(value -> 1L)
                        .orElse(0L))
                .sum();
        
        analysis.put("nonEmptyFields", nonEmptyFields);
        analysis.put("emptyFields", extractResults.size() - nonEmptyFields);
        
        return analysis;
    }

    /**
     * 创建模拟抽取结果
     */
    private List<BiCotResult> createMockExtractResults(String taskId, String objName, String dataType) {
        List<BiCotResult> results = new ArrayList<>();
        
        switch (dataType) {
            case "company":
                results.add(createMockResult(taskId, "companyName", objName));
                results.add(createMockResult(taskId, "establishDate", "2020-01-01"));
                results.add(createMockResult(taskId, "businessScope", "软件开发"));
                results.add(createMockResult(taskId, "registeredCapital", "1000万"));
                break;
                
            case "company_updated":
                results.add(createMockResult(taskId, "companyName", objName));
                results.add(createMockResult(taskId, "establishDate", "2020-01-01"));
                results.add(createMockResult(taskId, "businessScope", "软件开发、技术咨询"));
                results.add(createMockResult(taskId, "registeredCapital", "2000万"));
                results.add(createMockResult(taskId, "employeeCount", "50人"));
                break;
                
            case "person":
                results.add(createMockResult(taskId, "personName", objName));
                results.add(createMockResult(taskId, "age", "30"));
                results.add(createMockResult(taskId, "position", "软件工程师"));
                results.add(createMockResult(taskId, "department", "技术部"));
                break;
                
            case "product":
                results.add(createMockResult(taskId, "productName", objName));
                results.add(createMockResult(taskId, "price", "999.00"));
                results.add(createMockResult(taskId, "description", "高质量的测试产品"));
                results.add(createMockResult(taskId, "category", "电子产品"));
                break;
                
            default:
                results.add(createMockResult(taskId, "name", objName));
                results.add(createMockResult(taskId, "type", dataType));
        }
        
        return results;
    }

    /**
     * 创建模拟结果对象
     */
    private BiCotResult createMockResult(String taskId, String elementField, String elementResult) {
        BiCotResult result = new BiCotResult();
        result.setTaskId(taskId);
        result.setElementField(elementField);
        result.setElementName(elementField);
        result.setElementResult(elementResult);
        result.setCreateTime(LocalDateTime.now());
        result.setUpdateTime(LocalDateTime.now());
        return result;
    }
}
