package com.icarus.bi.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.bi.anno.ValidationGoups;
import com.icarus.bi.common.rest.BaseResponse;
import com.icarus.bi.entity.BiAlias;
import com.icarus.bi.service.biz.BiAliasBizService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 问数-别名表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Tag(name = "问数-别名")
@Slf4j
@RestController
@Validated
@RequestMapping("/bi/alias")
public class BiAliasController {

    @Resource
    private BiAliasBizService aliasBizService;


    @Operation(summary = "新增别名配置")
    @PostMapping("/add")
    public BaseResponse<Void> saveAlias(@RequestBody @Validated(ValidationGoups.Create.class) BiAlias req) {
        aliasBizService.saveAlias(req);
        return BaseResponse.ok(null);
    }

    @Operation(summary = "修改别名配置")
    @PostMapping("/update")
    public BaseResponse<Void> updateAlias(@RequestBody  @Validated(ValidationGoups.Update.class) BiAlias req) {
        aliasBizService.updateAlias(req);
        return BaseResponse.ok(null);
    }

    @Operation(summary = "根据全称获取别名配置")
    @GetMapping("/detail")
    public BaseResponse<BiAlias> getAliasByFullName(@RequestParam @NotBlank(message = "全称不能为空") String fullName) {
        return BaseResponse.ok(aliasBizService.getAliasByFullName(fullName));
    }

    @Operation(summary = "分页查询别名配置")
    @GetMapping("/page")
    public BaseResponse<Page<BiAlias>> pageQueryAlias(@RequestParam(defaultValue = "1") int currentPage,
                                                      @RequestParam(defaultValue = "10") int pageSize,
                                                      @RequestParam(required = false) String fullName,
                                                      @RequestParam(required = false) String aliasNames) {
        return BaseResponse.ok(aliasBizService.pageQueryAlias(currentPage, pageSize, fullName, aliasNames));
    }

    @Operation(summary = "禁用别名配置")
    @PostMapping("/disable")
    public BaseResponse<Void> deleteAlias(@NotNull(message = "id不能为空") Integer id) {
        aliasBizService.disableAlias(id);
        return BaseResponse.ok(null);
    }

    @Operation(summary = "启用别名配置")
    @PostMapping("/active")
    public BaseResponse<Void> activeAlias(@NotNull(message = "id不能为空") Integer id) {
        aliasBizService.activeAlias(id);
        return BaseResponse.ok(null);
    }

    @Operation(summary = "关键字搜索")
    @GetMapping("/search")
    public BaseResponse<BiAlias> search(@RequestParam @NotBlank(message = "关键字不能为空") String keyword) {
        return BaseResponse.ok(aliasBizService.search(keyword));
    }
}
