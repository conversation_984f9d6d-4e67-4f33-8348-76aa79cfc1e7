package com.icarus.bi.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.bi.anno.ValidationGoups;
import com.icarus.bi.common.rest.BaseResponse;
import com.icarus.bi.dto.BiCotTaskErrorResponse;
import com.icarus.bi.dto.BiCotTaskQueryDTO;
import com.icarus.bi.entity.BiCotTask;
import com.icarus.bi.vo.BiCotTaskPageVO;
import com.icarus.bi.exception.BiCotRuleNotActiveException;
import com.icarus.bi.service.IBiCotTaskService;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * COT任务表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Tag(name = "COT任务表")
@Slf4j
@RestController
@Validated
@RequestMapping("/bi/cotTask")
public class BiCotTaskController {

    @Resource
    private IBiCotTaskService biCotTaskService;

    @ApiOperation(value = "新增COT任务")
    @PostMapping("/add")
    public BaseResponse<Object> saveTask(@RequestBody @Validated(ValidationGoups.Create.class) BiCotTask req) {
        try {
            biCotTaskService.saveTask(req);
            return BaseResponse.ok("任务创建成功", req);
        } catch (BiCotRuleNotActiveException e) {
            log.warn("[新增COT任务]抽取规则未启用, error:{}", e.getMessage());
            BiCotTaskErrorResponse errorResponse = BiCotTaskErrorResponse.ruleNotActive(e.getMessage());
            return BaseResponse.badRequest(e.getMessage(), errorResponse);
        } catch (Exception e) {
            log.error("[新增COT任务]失败, error:{}", e.getMessage(), e);
            BiCotTaskErrorResponse errorResponse = BiCotTaskErrorResponse.generalError("TASK_CREATE_FAILED",
                    "新增任务失败: " + e.getMessage(), "请检查输入参数或联系管理员");
            return BaseResponse.serviceError("新增任务失败: " + e.getMessage(), errorResponse);
        }
    }

    @ApiOperation(value = "修改COT任务")
    @PostMapping("/update")
    public BaseResponse<Void> updateTask(@RequestBody @Validated(ValidationGoups.Update.class) BiCotTask req) {
        biCotTaskService.updateTask(req);
        return BaseResponse.ok(null);
    }

    @ApiOperation(value = "根据ID获取COT任务详情")
    @GetMapping("/detail")
    public BaseResponse<BiCotTask> getTaskById(@RequestParam @NotBlank(message = "ID不能为空") String id) {
        return BaseResponse.ok(biCotTaskService.getTaskById(id));
    }

    @ApiOperation(value = "根据taskID获取源文件Key")
    @GetMapping("/getFileKeyByTaskId")
    public BaseResponse<String> getFileKeyByTaskId(@RequestParam @NotBlank(message = "ID不能为空") String id) {
        return BaseResponse.ok(biCotTaskService.getTaskById(id).getSourceFileKey());
    }

    @ApiOperation(value = "分页查询COT任务（新接口，支持时间范围和扩展属性查询）")
    @PostMapping("/pageQuery")
    public BaseResponse<BiCotTaskPageVO> pageQueryTaskWithDTO(@RequestBody @Validated BiCotTaskQueryDTO queryDTO) {
        try {
            if (queryDTO == null) {
                return BaseResponse.badRequest("查询参数不能为空");
            }

            if (!queryDTO.isValidTimeRange()) {
                return BaseResponse.badRequest("时间范围设置不合理");
            }

            Page<BiCotTask> result = biCotTaskService.pageQueryTask(queryDTO);

            // 转换为VO对象
            BiCotTaskPageVO pageVO = BiCotTaskPageVO.fromPage(result);

            log.info("[分页查询COT任务-新接口]成功, 当前页:{}, 页大小:{}, 总记录数:{}",
                    queryDTO.getCurrentPage(), queryDTO.getPageSize(), result.getTotal());

            return BaseResponse.ok(pageVO);

        } catch (Exception e) {
            log.error("[分页查询COT任务-新接口]失败, queryDTO:{}, error:{}", queryDTO, e.getMessage(), e);
            return BaseResponse.serviceError("查询失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "更新任务执行状态")
    @PostMapping("/updateTaskStatus")
    public BaseResponse<Void> updateTaskStatus(@RequestParam @NotBlank(message = "ID不能为空") String id,
                                               @RequestParam @NotBlank(message = "执行状态不能为空") String taskStatus,
                                               @RequestParam(required = false) String errorReason) {
        biCotTaskService.updateTaskStatus(id, taskStatus, errorReason);
        return BaseResponse.ok(null);
    }

    @ApiOperation(value = "更新任务审核状态")
    @PostMapping("/updateAuditStatus")
    public BaseResponse<Void> updateAuditStatus(@RequestParam @NotBlank(message = "ID不能为空") String id,
                                                @RequestParam @NotBlank(message = "审核状态不能为空") String auditStatus) {
        biCotTaskService.updateAuditStatus(id, auditStatus);
        return BaseResponse.ok(null);
    }

    @ApiOperation(value = "根据抽取规则ID查询任务")
    @GetMapping("/getByRuleId")
    public BaseResponse<BiCotTask> getTaskByRuleId(@RequestParam @NotBlank(message = "抽取规则ID不能为空") String ruleId) {
        return BaseResponse.ok(biCotTaskService.getTaskByRuleId(ruleId));
    }

    @ApiOperation(value = "根据画布ID查询任务列表")
    @GetMapping("/getByCanvasId")
    public BaseResponse<Page<BiCotTask>> getTasksByCanvasId(@RequestParam @NotBlank(message = "画布ID不能为空") String canvasId,
                                                            @RequestParam(defaultValue = "1") int currentPage,
                                                            @RequestParam(defaultValue = "10") int pageSize) {
        return BaseResponse.ok(biCotTaskService.getTasksByCanvasId(canvasId, currentPage, pageSize));
    }

    @ApiOperation(value = "根据执行状态查询任务列表")
    @GetMapping("/getByStatus")
    public BaseResponse<Page<BiCotTask>> getTasksByStatus(@RequestParam @NotBlank(message = "执行状态不能为空") String taskStatus,
                                                          @RequestParam(defaultValue = "1") int currentPage,
                                                          @RequestParam(defaultValue = "10") int pageSize) {
        return BaseResponse.ok(biCotTaskService.getTasksByStatus(taskStatus, currentPage, pageSize));
    }

    @ApiOperation(value = "启动任务执行")
    @PostMapping("/start")
    public BaseResponse<Void> startTask(@RequestParam @NotBlank(message = "ID不能为空") String id) {
        biCotTaskService.startTask(id);
        return BaseResponse.ok(null);
    }

    @ApiOperation(value = "完成任务执行")
    @PostMapping("/finish")
    public BaseResponse<Void> finishTask(@RequestParam @NotBlank(message = "ID不能为空") String id) {
        biCotTaskService.finishTask(id);
        return BaseResponse.ok(null);
    }

    @ApiOperation(value = "删除COT任务")
    @DeleteMapping("/delete")
    public BaseResponse<Void> deleteTask(@RequestParam @NotBlank(message = "ID不能为空") String id) {
        biCotTaskService.deleteTask(id);
        return BaseResponse.ok(null);
    }
}