package com.icarus.bi.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.icarus.bi.common.rest.BaseResponse;
import com.icarus.bi.entity.BiCotResult;
import com.icarus.bi.entity.BiIdxData;
import com.icarus.bi.service.IBiCotResultService;
import com.icarus.bi.service.IBiIdxDataService;
import com.icarus.bi.service.biz.BiIdxDataBizService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 指标数据测试控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Api(description = "指标数据测试")
@Slf4j
@RestController
@Validated
@RequestMapping("/biIdxDataTest")
public class BiIdxDataTestController {

    @Resource
    private BiIdxDataBizService biIdxDataBizService;

    @Resource
    private IBiCotResultService biCotResultService;

    @Resource
    private IBiIdxDataService biIdxDataService;

    /**
     * 测试生成指标数据
     */
    @ApiOperation("测试生成指标数据")
    @PostMapping("/generateIdxData")
    public BaseResponse<Boolean> testGenerateIdxData(@RequestParam String taskId,
                                                     @RequestParam String ruleId) {
        try {
            log.info("[测试生成指标数据]开始, taskId:{}, ruleId:{}", taskId, ruleId);

            // 1. 获取任务的抽取结果
            List<BiCotResult> extractResults = biCotResultService.getResultsByTaskId(taskId, null);
            if (extractResults.isEmpty()) {
                return BaseResponse.badRequest("未找到任务的抽取结果, taskId: " + taskId);
            }

            // 2. 调用生成指标数据方法
            boolean success = biIdxDataBizService.generateIdxDataFromResults(taskId, ruleId, extractResults);

            log.info("[测试生成指标数据]完成, taskId:{}, ruleId:{}, 结果:{}", taskId, ruleId, success);

            return BaseResponse.ok("生成指标数据成功", success);

        } catch (Exception e) {
            log.error("[测试生成指标数据]失败, taskId:{}, ruleId:{}, error:{}", taskId, ruleId, e.getMessage(), e);
            return BaseResponse.serviceError("生成指标数据失败: " + e.getMessage());
        }
    }

    /**
     * 测试生成指标数据（手动传入抽取结果）
     */
    @ApiOperation("测试生成指标数据（手动传入抽取结果）")
    @PostMapping("/generateIdxDataWithResults")
    public BaseResponse<Boolean> testGenerateIdxDataWithResults(@RequestParam String taskId,
                                                                @RequestParam String ruleId,
                                                                @RequestBody String extractResultsJson) {
        try {
            log.info("[测试生成指标数据]开始, taskId:{}, ruleId:{}", taskId, ruleId);

            if (StrUtil.isBlank(extractResultsJson) || !JSONUtil.isTypeJSON(extractResultsJson)) {
                return BaseResponse.badRequest("抽取结果不是有效的JSON字符串");
            }

            // 解析抽取结果
            List<BiCotResult> extractResults = JSONUtil.toList(extractResultsJson, BiCotResult.class);

            // 调用生成指标数据方法
            boolean success = biIdxDataBizService.generateIdxDataFromResults(taskId, ruleId, extractResults);

            log.info("[测试生成指标数据]完成, taskId:{}, ruleId:{}, 结果:{}", taskId, ruleId, success);

            return BaseResponse.ok("生成指标数据成功", success);

        } catch (Exception e) {
            log.error("[测试生成指标数据]失败, taskId:{}, ruleId:{}, error:{}", taskId, ruleId, e.getMessage(), e);
            return BaseResponse.serviceError("生成指标数据失败: " + e.getMessage());
        }
    }

    /**
     * 测试selectByQurey方法 - 基础查询
     */
    @ApiOperation("测试selectByQurey方法 - 基础查询")
    @PostMapping("/selectByQuery")
    public BaseResponse<List<BiIdxData>> testSelectByQuery(@RequestBody BiIdxData query) {
        try {
            log.info("[测试selectByQuery]开始, query:{}", query);

            List<BiIdxData> result = biIdxDataService.selectByQurey(query);

            log.info("[测试selectByQuery]成功, 查询结果数量:{}", result.size());

            return BaseResponse.ok("查询成功", result);

        } catch (Exception e) {
            log.error("[测试selectByQuery]失败, error:{}", e.getMessage(), e);
            return BaseResponse.serviceError("查询失败: " + e.getMessage());
        }
    }

    /**
     * 测试selectByQurey方法 - 按指标配置ID查询
     */
    @ApiOperation("测试selectByQurey方法 - 按指标配置ID查询")
    @GetMapping("/selectByIdxConfId")
    public BaseResponse<List<BiIdxData>> testSelectByIdxConfId(@RequestParam String idxConfId) {
        try {
            log.info("[测试按指标配置ID查询]开始, idxConfId:{}", idxConfId);

            BiIdxData query = new BiIdxData();
            query.setIdxConfId(idxConfId);

            List<BiIdxData> result = biIdxDataService.selectByQurey(query);

            log.info("[测试按指标配置ID查询]成功, 查询结果数量:{}", result.size());

            return BaseResponse.ok("查询成功", result);

        } catch (Exception e) {
            log.error("[测试按指标配置ID查询]失败, idxConfId:{}, error:{}", idxConfId, e.getMessage(), e);
            return BaseResponse.serviceError("查询失败: " + e.getMessage());
        }
    }

    /**
     * 测试selectByQurey方法 - 按规则ID查询
     */
    @ApiOperation("测试selectByQurey方法 - 按规则ID查询")
    @GetMapping("/selectByRuleId")
    public BaseResponse<List<BiIdxData>> testSelectByRuleId(@RequestParam String ruleId) {
        try {
            log.info("[测试按规则ID查询]开始, ruleId:{}", ruleId);

            BiIdxData query = new BiIdxData();
            query.setRuleId(ruleId);

            List<BiIdxData> result = biIdxDataService.selectByQurey(query);

            log.info("[测试按规则ID查询]成功, 查询结果数量:{}", result.size());

            return BaseResponse.ok("查询成功", result);

        } catch (Exception e) {
            log.error("[测试按规则ID查询]失败, ruleId:{}, error:{}", ruleId, e.getMessage(), e);
            return BaseResponse.serviceError("查询失败: " + e.getMessage());
        }
    }

    /**
     * 测试selectByQurey方法 - 模糊查询
     */
    @ApiOperation("测试selectByQurey方法 - 模糊查询")
    @GetMapping("/selectByFuzzy")
    public BaseResponse<Map<String, Object>> testSelectByFuzzy(@RequestParam(required = false) String idxConfCode,
                                                               @RequestParam(required = false) String objName,
                                                               @RequestParam(required = false) String value) {
        try {
            log.info("[测试模糊查询]开始, idxConfCode:{}, objName:{}, value:{}", idxConfCode, objName, value);

            BiIdxData query = new BiIdxData();
            query.setIdxConfCode(idxConfCode);
            query.setObjName(objName);
            query.setValue(value);

            List<BiIdxData> result = biIdxDataService.selectByQurey(query);

            Map<String, Object> response = new HashMap<>();
            StringBuilder sb = new StringBuilder();
            sb.append("idxConfCode:").append(idxConfCode != null ? idxConfCode : "").append(", ");
            sb.append("objName:").append(objName != null ? objName : "").append(", ");
            sb.append("value:").append(value != null ? value : "");
            response.put("queryConditions", sb.toString());
            response.put("resultCount", result.size());
            response.put("results", result);

            log.info("[测试模糊查询]成功, 查询结果数量:{}", result.size());

            return BaseResponse.ok("查询成功", response);

        } catch (Exception e) {
            log.error("[测试模糊查询]失败, error:{}", e.getMessage(), e);
            return BaseResponse.serviceError("查询失败: " + e.getMessage());
        }
    }
}