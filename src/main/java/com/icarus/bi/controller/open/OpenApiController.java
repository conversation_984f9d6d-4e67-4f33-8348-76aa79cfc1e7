package com.icarus.bi.controller.open;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.icarus.bi.common.rest.BaseResponse;
import com.icarus.bi.controller.BiCotTaskController;
import com.icarus.bi.dto.BiCotTaskCmttPushReq;
import com.icarus.bi.entity.BiCotTask;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * OpenApi
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Tag(name = "问数-OpenApi")
@Slf4j
@RestController
@Validated
@RequestMapping("/bi/open")
public class OpenApiController {

    @Resource
    private BiCotTaskController cotTaskController;

    @ApiOperation(value = "接收CmttPush任务")
    @PostMapping("/cmttPush")
    public BaseResponse<Object> receiveTaskFormCmtt(@RequestBody @Valid BiCotTaskCmttPushReq req) {
        try {
            log.info("[接收CmttPush任务]开始, detail.name:{}, files数量:{}",
                    ObjectUtil.isNotNull(req.getDetail()) ? req.getDetail().getName() : "未知",
                    ObjectUtil.isNotNull(req.getFiles()) ? req.getFiles().size() : 0);

            // 验证请求参数
            if (CollectionUtil.isEmpty(req.getFiles())) {
                log.warn("[接收CmttPush任务]files为空");
                return BaseResponse.badRequest("files不能为空");
            }

            if (ObjectUtil.isNull(req.getDetail())) {
                log.warn("[接收CmttPush任务]detail为空");
                return BaseResponse.badRequest("detail不能为空");
            }

            List<BiCotTask> createdTasks = new ArrayList<>();
            List<Map<String, Object>> failedFiles = new ArrayList<>();

            // 为每个FilesDTO生成一个任务
            for (int i = 0; i < req.getFiles().size(); i++) {
                BiCotTaskCmttPushReq.FilesDTO file = req.getFiles().get(i);
                try {
                    log.debug("[接收CmttPush任务]处理文件 {}/{}, fileId:{}, fileName:{}",
                            i + 1, req.getFiles().size(), file.getId(), file.getName());

                    BiCotTask biCotTask = createBiCotTaskFromFile(req, file);

                    // 保存任务
                    BaseResponse<Object> saveResult = cotTaskController.saveTask(biCotTask);
                    if (saveResult.getCode() == 200 && saveResult.getData() != null) {
                        if (saveResult.getData() instanceof BiCotTask) {
                            BiCotTask data = (BiCotTask) saveResult.getData();
                            createdTasks.add(data);
                            log.debug("[接收CmttPush任务]文件任务创建成功, taskId:{}, fileId:{}, fileName:{}",
                                    data.getId(), file.getId(), file.getName());
                        }else {
                            log.error("[接收CmttPush任务]文件任务创建失败, fileId:{}, fileName:{}, error:{}",
                                    file.getId(), file.getName(), "数据转换失败");
                        }

                    } else {
                        Map<String, Object> failedFile = new HashMap<>();
                        failedFile.put("fileId", file.getId());
                        failedFile.put("fileName", file.getName());
                        failedFile.put("error", saveResult.getStatusText());
                        failedFiles.add(failedFile);

                        log.error("[接收CmttPush任务]文件任务创建失败, fileId:{}, fileName:{}, error:{}",
                                file.getId(), file.getName(), saveResult.getStatusText());
                    }

                } catch (Exception e) {
                    Map<String, Object> failedFile = new HashMap<>();
                    failedFile.put("fileId", file.getId());
                    failedFile.put("fileName", file.getName());
                    failedFile.put("error", e.getMessage());
                    failedFiles.add(failedFile);

                    log.error("[接收CmttPush任务]处理文件异常, fileId:{}, fileName:{}, error:{}",
                            file.getId(), file.getName(), e.getMessage(), e);
                }
            }

            // 构建返回结果
            Map<String, Object> result = buildResponseResult(req, createdTasks, failedFiles);

            log.info("[接收CmttPush任务]完成, detail.name:{}, 总文件数:{}, 成功创建:{}, 失败:{}",
                    req.getDetail().getName(), req.getFiles().size(), createdTasks.size(), failedFiles.size());

            return determineResponseStatus(createdTasks, failedFiles, result);

        } catch (Exception e) {
            log.error("[接收CmttPush任务]处理异常, error:{}", e.getMessage(), e);
            return BaseResponse.serviceError("处理CmttPush任务失败: " + e.getMessage());
        }
    }

    /**
     * 根据文件信息创建BiCotTask
     */
    private BiCotTask createBiCotTaskFromFile(BiCotTaskCmttPushReq req, BiCotTaskCmttPushReq.FilesDTO file) {
        BiCotTask biCotTask = new BiCotTask();
        // 设置源文件key
        biCotTask.setSourceFileKey(file.getMinioFileKey());
        biCotTask.setExtraAttrs(JSONUtil.toJsonStr(req));
        return biCotTask;
    }

    /**
     * 构建返回结果
     */
    private Map<String, Object> buildResponseResult(BiCotTaskCmttPushReq req,
                                                   List<BiCotTask> createdTasks,
                                                   List<Map<String, Object>> failedFiles) {
        Map<String, Object> result = new HashMap<>();

        // 基础统计信息
        result.put("totalFiles", req.getFiles().size());
        result.put("successCount", createdTasks.size());
        result.put("failedCount", failedFiles.size());

        // 请求信息
        Optional.ofNullable(req.getDetail()).ifPresent(detail -> {
            Map<String, String> requestInfo = new HashMap<>();
            requestInfo.put("detailId", Optional.ofNullable(detail.getId()).orElse(""));
            requestInfo.put("detailName", Optional.ofNullable(detail.getName()).orElse(""));
            requestInfo.put("sourceInstitution", Optional.ofNullable(detail.getSourceInstitution()).orElse(""));
            requestInfo.put("subject", Optional.ofNullable(detail.getSubject()).orElse(""));
            result.put("requestInfo", requestInfo);
        });

        // 成功创建的任务信息
        result.put("createdTasks", createdTasks.stream().map(task -> {
            Map<String, Object> taskInfo = new HashMap<>();
            taskInfo.put("taskId", task.getId());
            taskInfo.put("ruleName", task.getRuleName());
            taskInfo.put("canvasId", task.getCanvasId());
            taskInfo.put("sourceFileKey", task.getSourceFileKey());
            taskInfo.put("taskStatus", task.getTaskStatus());
            taskInfo.put("auditStatus", task.getAuditStatus());
            taskInfo.put("createTime", task.getCreateTime());
            return taskInfo;
        }).collect(Collectors.toList()));

        // 失败的文件信息
        if (!failedFiles.isEmpty()) {
            result.put("failedFiles", failedFiles);
        }

        return result;
    }

    /**
     * 根据处理结果确定响应状态
     */
    private BaseResponse<Object> determineResponseStatus(List<BiCotTask> createdTasks,
                                                        List<Map<String, Object>> failedFiles,
                                                        Map<String, Object> result) {
        if (failedFiles.isEmpty()) {
            return BaseResponse.ok("所有任务创建成功", result);
        } else if (createdTasks.isEmpty()) {
            return BaseResponse.serviceError("所有任务创建失败", result);
        } else {
            return BaseResponse.ok("部分任务创建成功", result);
        }
    }

}