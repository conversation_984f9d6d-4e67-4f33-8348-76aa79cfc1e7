package com.icarus.bi.controller.open;

import com.icarus.bi.common.rest.BaseResponse;
import com.icarus.bi.dto.BiCotTaskCmttPushReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * OpenApi测试控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-17
 */
@Api(description = "OpenApi测试控制器")
@Slf4j
@RestController
@RequestMapping("/openApi/test")
public class OpenApiTestController {

    @Resource
    private OpenApiController openApiController;

    @ApiOperation(value = "测试CmttPush任务创建（单文件）")
    @PostMapping("/testSingleFileCmttPush")
    public BaseResponse<Object> testSingleFileCmttPush() {
        try {
            log.info("[测试单文件CmttPush]开始");
            
            BiCotTaskCmttPushReq req = createSingleFileTestRequest();
            
            BaseResponse<Object> result = openApiController.receiveTaskFormCmtt(req);
            
            log.info("[测试单文件CmttPush]完成, 结果:{}", result.getCode());
            
            return result;
            
        } catch (Exception e) {
            log.error("[测试单文件CmttPush]失败, error:{}", e.getMessage(), e);
            return BaseResponse.serviceError("测试失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "测试CmttPush任务创建（多文件）")
    @PostMapping("/testMultiFileCmttPush")
    public BaseResponse<Object> testMultiFileCmttPush(
            @ApiParam(value = "文件数量") @RequestParam(defaultValue = "3") Integer fileCount) {
        try {
            log.info("[测试多文件CmttPush]开始, fileCount:{}", fileCount);
            
            BiCotTaskCmttPushReq req = createMultiFileTestRequest(fileCount);
            
            BaseResponse<Object> result = openApiController.receiveTaskFormCmtt(req);
            
            log.info("[测试多文件CmttPush]完成, fileCount:{}, 结果:{}", fileCount, result.getCode());
            
            return result;
            
        } catch (Exception e) {
            log.error("[测试多文件CmttPush]失败, fileCount:{}, error:{}", fileCount, e.getMessage(), e);
            return BaseResponse.serviceError("测试失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "测试真实数据格式")
    @PostMapping("/testRealDataFormat")
    public BaseResponse<Object> testRealDataFormat() {
        try {
            log.info("[测试真实数据格式]开始");
            
            BiCotTaskCmttPushReq req = createRealDataFormatRequest();
            
            BaseResponse<Object> result = openApiController.receiveTaskFormCmtt(req);
            
            log.info("[测试真实数据格式]完成, 结果:{}", result.getCode());
            
            return result;
            
        } catch (Exception e) {
            log.error("[测试真实数据格式]失败, error:{}", e.getMessage(), e);
            return BaseResponse.serviceError("测试失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "生成测试数据示例")
    @GetMapping("/generateTestDataExamples")
    public BaseResponse<Map<String, Object>> generateTestDataExamples() {
        Map<String, Object> examples = new HashMap<>();
        
        // 单文件示例
        examples.put("singleFileExample", createSingleFileTestRequest());
        
        // 多文件示例
        examples.put("multiFileExample", createMultiFileTestRequest(3));
        
        // 真实数据格式示例
        examples.put("realDataExample", createRealDataFormatRequest());
        
        Map<String, String> descriptions = new HashMap<>();
        descriptions.put("singleFileExample", "单个文件的测试请求，会创建1个任务");
        descriptions.put("multiFileExample", "多个文件的测试请求，会创建3个任务");
        descriptions.put("realDataExample", "基于真实数据格式的测试请求");
        examples.put("说明", descriptions);
        
        return BaseResponse.ok("测试数据示例", examples);
    }

    /**
     * 创建单文件测试请求
     */
    private BiCotTaskCmttPushReq createSingleFileTestRequest() {
        BiCotTaskCmttPushReq req = new BiCotTaskCmttPushReq();
        
        // 创建detail
        BiCotTaskCmttPushReq.DetailDTO detail = new BiCotTaskCmttPushReq.DetailDTO();
        detail.setId("test-detail-001");
        detail.setName("测试文档集合");
        detail.setSourceInstitution("测试机构");
        detail.setSubject("TEST_SUBJECT");
        detail.setCollectionPath("测试路径");
        detail.setCollectionPathUrl("https://test.example.com");
        req.setDetail(detail);
        
        // 创建单个文件
        List<BiCotTaskCmttPushReq.FilesDTO> files = new ArrayList<>();
        BiCotTaskCmttPushReq.FilesDTO file = new BiCotTaskCmttPushReq.FilesDTO();
        file.setId("test-file-001");
        file.setName("测试文档.pdf");
        file.setCorpusId("test-corpus-001");
        file.setType("pdf");
        file.setFileSize("1.5MB");
        file.setMinioFileKey("test/documents/test-doc.pdf");
        file.setProperty("文档");
        files.add(file);
        
        req.setFiles(files);
        return req;
    }

    /**
     * 创建多文件测试请求
     */
    private BiCotTaskCmttPushReq createMultiFileTestRequest(Integer fileCount) {
        BiCotTaskCmttPushReq req = new BiCotTaskCmttPushReq();
        
        // 创建detail
        BiCotTaskCmttPushReq.DetailDTO detail = new BiCotTaskCmttPushReq.DetailDTO();
        detail.setId("test-detail-multi");
        detail.setName("多文件测试集合");
        detail.setSourceInstitution("测试机构");
        detail.setSubject("MULTI_TEST");
        detail.setCollectionPath("多文件测试路径");
        detail.setCollectionPathUrl("https://test.example.com/multi");
        req.setDetail(detail);
        
        // 创建多个文件
        List<BiCotTaskCmttPushReq.FilesDTO> files = new ArrayList<>();
        for (int i = 1; i <= fileCount; i++) {
            BiCotTaskCmttPushReq.FilesDTO file = new BiCotTaskCmttPushReq.FilesDTO();
            file.setId("test-file-" + String.format("%03d", i));
            file.setName("测试文档" + i + ".pdf");
            file.setCorpusId("test-corpus-" + String.format("%03d", i));
            file.setType("pdf");
            file.setFileSize((1.0 + i * 0.1) + "MB");
            file.setMinioFileKey("test/documents/test-doc-" + i + ".pdf");
            file.setProperty("文档");
            files.add(file);
        }
        
        req.setFiles(files);
        return req;
    }

    /**
     * 创建基于真实数据格式的测试请求
     */
    private BiCotTaskCmttPushReq createRealDataFormatRequest() {
        BiCotTaskCmttPushReq req = new BiCotTaskCmttPushReq();
        
        // 创建detail（基于真实数据结构）
        BiCotTaskCmttPushReq.DetailDTO detail = new BiCotTaskCmttPushReq.DetailDTO();
        detail.setCorpusCollectionConfigId("66f0e14eaaf05fb7d1111111");
        detail.setSourceInstitutionUrl("https://www.fullgoal.com.cn");
        detail.setSubSourceInstitution("富国基金管理有限公司");
        detail.setCollectionPath("招募说明书");
        detail.setSubject("FINANCIAL_COMMON");
        detail.setPublishDate("1755014400000");
        detail.setDescription("富国中证新华社民族品牌工程交易型开放式指数证券投资基金招募说明书（更新）（2025年第3号）");
        detail.setOfficialNumber("");
        detail.setUpdateTime("1755412522837");
        List<String> tags = new ArrayList<>();
        tags.add("招募说明书");
        tags.add("富国基金管理有限公司");
        detail.setTags(tags);
        detail.setCollectionDetailPath("招募说明书");
        detail.setSourceEntity("基金公司/富国基金管理有限公司");
        detail.setSourceInstitution("基金公司");
        detail.setCreateTime("1755412522837");
        detail.setCorpusDatabaseId("689fec78e4b09a9d9ff8c14b");
        detail.setName("富国中证新华社民族品牌工程交易型开放式指数证券投资基金招募说明书（更新）（2025年第3号）");
        detail.setId("68a1782ab35c626828a5db05");
        detail.setCollectionPathUrl("https://www.fullgoal.com.cn/main/InforDisclosure/Notice/index.html");
        req.setDetail(detail);
        
        // 创建文件（基于真实数据结构）
        List<BiCotTaskCmttPushReq.FilesDTO> files = new ArrayList<>();
        BiCotTaskCmttPushReq.FilesDTO file = new BiCotTaskCmttPushReq.FilesDTO();
        file.setId("68a1782ab35c626828a5db06");
        file.setUpdateTime("1755412522925");
        file.setCorpusId("68a1782ab35c626828a5db05");
        file.setName("富国中证新华社民族品牌工程交易型开放式指数证券投资基金招募说明书（更新）（2025年第3号）2025-08-13_下载文件.pdf");
        file.setProperty("文档");
        file.setType("pdf");
        file.setFileSize("1.7MB");
        file.setMinioFileKey("crawler/510/8a8be0b2/富国中证新华社民族品牌工程交易型开放式指数证券投资基金招募说明书（更新）（2025年第3号）2025-08-13_下载文件.pdf");
        files.add(file);
        
        req.setFiles(files);
        return req;
    }
}
