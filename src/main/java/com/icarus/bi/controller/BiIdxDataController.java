package com.icarus.bi.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.bi.anno.ValidationGoups;
import com.icarus.bi.common.rest.BaseResponse;
import com.icarus.bi.dto.BiIdxDataDto;
import com.icarus.bi.dto.QueryCriteria;
import com.icarus.bi.entity.BiIdxData;
import com.icarus.bi.service.biz.BiIdxDataBizService;
import com.icarus.bi.vo.BiIdxDataSetVo;
import com.icarus.bi.vo.ExtraResultVo;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 指标-数据表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-16
 */

@Tag(name = "指标-数据")
@Slf4j
@RestController
@Validated
@RequestMapping("/bi/idxData")
public class BiIdxDataController {

    @Resource
    private BiIdxDataBizService biIdxDataService;

    /**
     * 根据指定的指标配置和文件获取所有指标值
     */
//    @ApiOperation(value = "根据指定的指标配置和文件获取所有指标值")
//    @PostMapping("/queryCriteria")
//    public BaseResponse<List<BiIdxData>> queryCriteria(@RequestBody QueryCriteria input) {
//        log.info("根据配置ID查询指标数据，请求参数：{}", input);
//        List<BiIdxData> result = biIdxDataService.queryCriteria(input);
//        return BaseResponse.ok(result);
//    }

    @ApiOperation(value = "根据指定的指标配置和文件获取所有指标值")
    @PostMapping("/queryCriteria")
    public BaseResponse<List<BiIdxDataSetVo>> queryCriteria(@RequestBody QueryCriteria input) {
        log.info("根据配置ID查询指标数据，请求参数：{}", input);
        List<BiIdxDataSetVo> result = biIdxDataService.queryCriteria(input);
        return BaseResponse.ok(result);
    }

    @ApiOperation(value = "根据指定的指标配置和文件获取所有指标值并拼成产品信息")
    @PostMapping("/queryProducts")
    public BaseResponse<List<JSONObject>> queryProducts(@RequestBody QueryCriteria input) {
        log.info("根据配置ID查询指标数据，请求参数：{}", input);
        List<BiIdxDataSetVo> result = biIdxDataService.queryCriteria(input);
        return BaseResponse.ok(result.stream()
                .map(this::convert2ProductInfo)
                .filter(Objects::nonNull)
                .collect(Collectors.toList()));
    }

    private JSONObject convert2ProductInfo(BiIdxDataSetVo biIdxDataSetVo) {
        if (StrUtil.isBlank(biIdxDataSetVo.getProductName())) {
            return null;
        }
        if (biIdxDataSetVo.getData().stream().anyMatch(biIdxData -> biIdxData.getIdxConfCode().equals("productName") && StrUtil.isBlank(biIdxData.getValue()))) {
            return null;
        }

        JSONObject product = new JSONObject();
        product.set("产品名称", biIdxDataSetVo.getProductName());
        biIdxDataSetVo.getData().forEach(biIdxData -> product.set(biIdxData.getIdxConfName(), biIdxData.getValue()));
        return product;
    }

    /**
     * 新增指标数据
     */
    @ApiOperation(value = "新增指标数据")
    @PostMapping("/add")
    public BaseResponse<Void> saveIdxData(@RequestBody @Validated(ValidationGoups.Create.class) BiIdxData bid) {
        biIdxDataService.saveBid(bid);
        return BaseResponse.ok(null);
    }

    @Operation(summary = "修改指标数据")
    @PostMapping("/update")
    public BaseResponse<Void> updateIdxData(@RequestBody  @Validated(ValidationGoups.Update.class) BiIdxData bid){
        biIdxDataService.updateBid(bid);
        return BaseResponse.ok(null);
    }

    @Operation(summary = "删除指标数据")
    @PostMapping("/delete")
    public BaseResponse<Void> deleteIdxData(@NotNull(message = "id不能为空") Integer id) {
        biIdxDataService.deleteBid(id);
        return BaseResponse.ok(null);
    }

    @Operation(summary = "关键字搜索")
    @GetMapping("/search")
    public BaseResponse<List<BiIdxDataDto>> search(@RequestParam @NotBlank(message = "关键字不能为空") String keyword) {
        List<BiIdxDataDto> dtos = biIdxDataService.search(keyword);
        return BaseResponse.ok(dtos);
    }

    @Operation(summary = "分页查询指标数据")
    @GetMapping("/page")
    public BaseResponse<Page<BiIdxDataDto>> pageQueryAlias(@RequestParam(defaultValue = "1") int currentPage,
                                                      @RequestParam(defaultValue = "10") int pageSize,
                                                      @RequestParam(required = false) String idxConfId,
                                                      @RequestParam(required = false) String idxConfCode,
                                                      @RequestParam(required = false) String value) {
        Page<BiIdxDataDto> biIdxDataDtoPage = biIdxDataService.pageQueryBid(currentPage, pageSize, idxConfId, idxConfCode, value);
        return BaseResponse.ok(biIdxDataDtoPage);
    }

    /**
     * 获取指标数据展示宽表
     * @return
     */
    @Operation(summary = "获取指标数据展示宽表")
    @GetMapping("/getBiIdxDataForWideTable")
    public BaseResponse<ExtraResultVo> getBiIdxDataForWideTable(){
        return BaseResponse.ok(biIdxDataService.getBiIdxDataForWideTable());
    }
}
