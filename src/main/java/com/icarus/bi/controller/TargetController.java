package com.icarus.bi.controller;

import cn.hutool.json.JSONUtil;
import com.icarus.bi.common.rest.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 目标查询条件组装控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Api(description = "目标查询条件组装")
@Slf4j
@RestController
@Validated
@RequestMapping("/bi/target")
public class TargetController {

    @ApiOperation("查询条件组装")
    @PostMapping("/assembleConditions")
    public BaseResponse<Target> assembleConditions(@RequestBody AssembleRequest request) {
        try {
            log.info("[查询条件组装]开始处理请求");

            // 构建字典映射
            Map<String, String> dictionaryMap = request.getDictionaryList().stream()
                    .collect(Collectors.toMap(
                            DictionaryItem::getCode,
                            DictionaryItem::getName,
                            (existing, replacement) -> existing
                    ));

            // 构建条件映射
            Map<String, String> conditionMap = new HashMap<>();
            conditionMap.put("=", "等于");
            conditionMap.put("!=", "不等于");
            conditionMap.put("IN", "包含");
            conditionMap.put("NOT_IN", "不包含");
            conditionMap.put(">", "大于");
            conditionMap.put("<", "小于");
            conditionMap.put("<=", "小于等于");
            conditionMap.put(">=", "大于等于");
            conditionMap.put("LIKE", "模糊匹配");
            conditionMap.put("NOT_LIKE", "不匹配");

            // 构建选项列表
            List<Option> options = request.getDictionaryList().stream()
                    .map(item -> new Option(item.getName(), item.getName()))
                    .collect(Collectors.toList());

            // 构建目标对象
            Target target = new Target();
            target.setType("dynamicForm");
            target.setTitle("查询条件组装");

            // 构建数据源
            List<DataSource> dataSourceList = Arrays.asList(
                    new DataSource("queryField", "查询字段", "select", "130px",
                            new Props(options)),
                    new DataSource("condition", "参与条件", "select", "90px",
                            new Props(Arrays.asList(
                                    new Option("等于", "等于"),
                                    new Option("不等于", "不等于"),
                                    new Option("包含", "包含"),
                                    new Option("不包含", "不包含"),
                                    new Option("大于", "大于"),
                                    new Option("小于", "小于"),
                                    new Option("小于等于", "小于等于"),
                                    new Option("大于等于", "大于等于"),
                                    new Option("模糊匹配", "模糊匹配"),
                                    new Option("不匹配", "不匹配")
                            ))),
                    new DataSource("conditionValue", "条件值", "input", "130px", null)
            );
            target.setDataSource(dataSourceList);
            // 构建表单数据
            List<FormData> formDataList = new ArrayList<>();
            Params params = JSONUtil.toBean(request.getParams(), Params.class);
            for (int i = 0; i < params.getConditions().size(); i++) {
                Condition condition = params.getConditions().get(i);
                FormData formData = new FormData();
                formData.setId(i + 1);
                formData.setQueryField(dictionaryMap.get(condition.getField()));
                formData.setCondition(conditionMap.get(condition.getOperator()));
                formData.setConditionValue(condition.getValue());
                formDataList.add(formData);
            }
            target.setFormData(formDataList);

            log.info("[查询条件组装]处理完成, 条件数量:{}", formDataList.size());
            return BaseResponse.ok(target);

        } catch (Exception e) {
            log.error("[查询条件组装]处理失败, error:{}", e.getMessage(), e);
            return BaseResponse.serviceError("查询条件组装失败: " + e.getMessage());
        }
    }

    @Data
    public static class AssembleRequest {
        private String params;
        private List<DictionaryItem> dictionaryList;
    }

    @Data
    public static class Params {
        private List<String> productName;
        private List<Condition> conditions;
        private List<String> targets;
        private String reasoning;
    }

    @Data
    public static class Condition {
        private String field;
        private String operator;
        private String value;
        private String logic;
    }

    @Data
    public static class DictionaryItem {
        private String id;
        private String code;
        private String name;
        private Integer order;
        private Boolean ifActive;
        private String ruleId;
        private String elementField;
        private String remark;
        private String elementScene;
        private String elementScopeExp;
        private String createTime;
        private String updateTime;
        private Boolean ifOverwrite;
        private Boolean ifPrimaryKey;
    }

    @Data
    public static class Target {
        private String type;
        private String title;
        private List<FormData> formData;
        private List<DataSource> dataSource;
    }

    @Data
    public static class FormData {
        private Integer id;
        private String queryField;
        private String condition;
        private String conditionValue;
    }

    @Data
    public static class DataSource {
        private String dataIndex;
        private String title;
        private String components;
        private String width;
        private Props props;

        public DataSource(String dataIndex, String title, String components, String width, Props props) {
            this.dataIndex = dataIndex;
            this.title = title;
            this.components = components;
            this.width = width;
            this.props = props;
        }
    }

    @Data
    public static class Props {
        private List<Option> options;

        public Props(List<Option> options) {
            this.options = options;
        }
    }

    @Data
    public static class Option {
        private String label;
        private String value;

        public Option(String label, String value) {
            this.label = label;
            this.value = value;
        }
    }
}