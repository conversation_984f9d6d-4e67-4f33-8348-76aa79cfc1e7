package com.icarus.bi.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.bi.anno.ValidationGoups;
import com.icarus.bi.common.rest.BaseResponse;
import com.icarus.bi.entity.BiCotRule;
import com.icarus.bi.dto.BiCotRuleQueryDTO;
import com.icarus.bi.service.IBiCotRuleService;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 抽取规则配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Tag(name = "抽取规则配置表")
@Slf4j
@RestController
@Validated
@RequestMapping("/bi/cotRule")
public class BiCotRuleController {

    @Resource
    private IBiCotRuleService biCotRuleService;

    @ApiOperation(value = "新增抽取规则")
    @PostMapping("/add")
    public BaseResponse<Void> saveRule(@RequestBody @Validated(ValidationGoups.Create.class) BiCotRule req) {
        biCotRuleService.saveRule(req);
        return BaseResponse.ok(null);
    }

    @ApiOperation(value = "修改抽取规则")
    @PostMapping("/update")
    public BaseResponse<Void> updateRule(@RequestBody @Validated(ValidationGoups.Update.class) BiCotRule req) {
        biCotRuleService.updateRule(req);
        return BaseResponse.ok(null);
    }

    @ApiOperation(value = "根据ID获取抽取规则详情")
    @GetMapping("/detail")
    public BaseResponse<BiCotRule> getRuleById(@RequestParam @NotBlank(message = "ID不能为空") String id) {
        return BaseResponse.ok(biCotRuleService.getRuleById(id));
    }

    @ApiOperation(value = "分页查询抽取规则（新接口，支持时间范围和排序）")
    @PostMapping("/page")
    public BaseResponse<Page<BiCotRule>> pageQueryRuleWithDTO(@RequestBody @Validated BiCotRuleQueryDTO queryDTO) {
        try {
            if (queryDTO == null) {
                return BaseResponse.badRequest("查询参数不能为空");
            }

            if (!queryDTO.isValidTimeRange()) {
                return BaseResponse.badRequest("时间范围设置不合理");
            }

            Page<BiCotRule> result = biCotRuleService.pageQueryRule(queryDTO);

            log.info("[分页查询抽取规则-新接口]成功, 当前页:{}, 页大小:{}, 总记录数:{}",
                    queryDTO.getCurrentPage(), queryDTO.getPageSize(), result.getTotal());

            return BaseResponse.ok(result);

        } catch (Exception e) {
            log.error("[分页查询抽取规则-新接口]失败, queryDTO:{}, error:{}", queryDTO, e.getMessage(), e);
            return BaseResponse.serviceError("查询失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "启用抽取规则")
    @PostMapping("/active")
    public BaseResponse<Void> activeRule(@RequestParam @NotBlank(message = "ID不能为空") String id) {
        biCotRuleService.activeRule(id);
        return BaseResponse.ok(null);
    }

    @ApiOperation(value = "禁用抽取规则")
    @PostMapping("/disable")
    public BaseResponse<Void> disableRule(@RequestParam @NotBlank(message = "ID不能为空") String id) {
        biCotRuleService.disableRule(id);
        return BaseResponse.ok(null);
    }

    @ApiOperation(value = "根据规则Code查询抽取规则")
    @GetMapping("/getByCode")
    public BaseResponse<BiCotRule> getRuleByCode(@RequestParam @NotBlank(message = "规则Code不能为空") String code) {
        return BaseResponse.ok(biCotRuleService.getRuleByCode(code));
    }

    @ApiOperation(value = "根据画布ID查询规则列表")
    @GetMapping("/getByCanvasId")
    public BaseResponse<Page<BiCotRule>> getRulesByCanvasId(@RequestParam @NotBlank(message = "画布ID不能为空") String canvasId,
                                                            @RequestParam(defaultValue = "1") int currentPage,
                                                            @RequestParam(defaultValue = "10") int pageSize) {
        return BaseResponse.ok(biCotRuleService.getRulesByCanvasId(canvasId, currentPage, pageSize));
    }

    @ApiOperation(value = "根据启用状态查询规则列表")
    @GetMapping("/getByActiveStatus")
    public BaseResponse<Page<BiCotRule>> getRulesByActiveStatus(@RequestParam Boolean ifActive,
                                                                @RequestParam(defaultValue = "1") int currentPage,
                                                                @RequestParam(defaultValue = "10") int pageSize) {
        return BaseResponse.ok(biCotRuleService.getRulesByActiveStatus(ifActive, currentPage, pageSize));
    }

    @ApiOperation(value = "更新画布版本号")
    @PostMapping("/updateCanvasVersion")
    public BaseResponse<Void> updateCanvasVersion(@RequestParam @NotBlank(message = "ID不能为空") String id,
                                                  @RequestParam @NotBlank(message = "画布版本号不能为空") String canvasVersion) {
        biCotRuleService.updateCanvasVersion(id, canvasVersion);
        return BaseResponse.ok(null);
    }

    @ApiOperation(value = "根据画布版本号查询规则列表")
    @GetMapping("/getByCanvasVersion")
    public BaseResponse<Page<BiCotRule>> getRulesByCanvasVersion(@RequestParam @NotBlank(message = "画布版本号不能为空") String canvasVersion,
                                                                 @RequestParam(defaultValue = "1") int currentPage,
                                                                 @RequestParam(defaultValue = "10") int pageSize) {
        return BaseResponse.ok(biCotRuleService.getRulesByCanvasVersion(canvasVersion, currentPage, pageSize));
    }

    @ApiOperation(value = "删除抽取规则")
    @DeleteMapping("/delete")
    public BaseResponse<Void> deleteRule(@RequestParam @NotBlank(message = "ID不能为空") String id) {
        biCotRuleService.deleteRule(id);
        return BaseResponse.ok(null);
    }
}
