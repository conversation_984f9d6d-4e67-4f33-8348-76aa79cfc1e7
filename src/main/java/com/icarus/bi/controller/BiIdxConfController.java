package com.icarus.bi.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icarus.bi.dto.BiIdxConfDto;
import com.icarus.bi.anno.ValidationGoups;
import com.icarus.bi.common.rest.BaseResponse;
import com.icarus.bi.entity.BiIdxConf;
import com.icarus.bi.service.IBiIdxConfService;
import com.icarus.bi.service.biz.BiIdxConfBizService;
import io.swagger.annotations.ApiOperation;
import com.icarus.bi.service.biz.BiIdxConfBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

@Tag(name = "指标-配置")
@Slf4j
@RestController
@Validated
@RequestMapping("/bi/idxConf")
public class BiIdxConfController {

    @Resource
    private BiIdxConfBizService biIdxConfBizService;

    /**
     * 根据场景筛选所有指标配置信息
     */
    @ApiOperation(value = "根据场景筛选所有指标配置信息")
    @GetMapping("/queryByScene")
    public BaseResponse<List<BiIdxConf>> queryByScene(@RequestParam(name = "scene",required = false) String scene) {
        log.info("根据场景筛选指标配置信息，请求参数：{}", scene);
        //mock数据
        scene = StringUtils.hasText(scene)? scene : "";
        List<BiIdxConf> biIdxConfList = biIdxConfBizService.queryByScene(scene);
        if(Objects.isNull(biIdxConfList)){
            return BaseResponse.serviceError("未找到该场景的指标配置信息");
        }
        return BaseResponse.ok(biIdxConfList);
    }

    /**
     * 新增指标配置
     */
    @ApiOperation(value = "新增指标配置")
    @PostMapping("/add")
    public BaseResponse<Void> saveIdxConf(@RequestBody @Validated(ValidationGoups.Create.class) BiIdxConf bic) {
        biIdxConfBizService.saveBic(bic);
        return BaseResponse.ok(null);
    }

    @Operation(summary = "修改指标配置数据")
    @PostMapping("/update")
    public BaseResponse<Void> updateIdxConf(@RequestBody  @Validated(ValidationGoups.Update.class) BiIdxConf bic){
        biIdxConfBizService.updateBic(bic);
        return BaseResponse.ok(null);
    }

    @Operation(summary = "删除指标配置数据")
    @PostMapping("/delete")
    public BaseResponse<Void> deleteIdxConf(@NotNull(message = "id不能为空") Integer id) {
        biIdxConfBizService.deleteBic(id);
        return BaseResponse.ok(null);
    }

    @Operation(summary = "禁用指标配置配置")
    @PostMapping("/disable")
    public BaseResponse<Void> disableIdxConf(@NotNull(message = "id不能为空") Integer id) {
        biIdxConfBizService.disableAlias(id);
        return BaseResponse.ok(null);
    }

    @Operation(summary = "启用指标配置配置")
    @PostMapping("/active")
    public BaseResponse<Void> activeIdxConf(@NotNull(message = "id不能为空") Integer id) {
        biIdxConfBizService.activeAlias(id);
        return BaseResponse.ok(null);
    }

    @Operation(summary = "关键字搜索")
    @GetMapping("/search")
    public BaseResponse<List<BiIdxConfDto>> search(@RequestParam @NotBlank(message = "关键字不能为空") String keyword) {
        List<BiIdxConfDto> dtos = biIdxConfBizService.search(keyword);
        return BaseResponse.ok(dtos);
    }

    @Operation(summary = "分页查询指标数据")
    @GetMapping("/page")
    public BaseResponse<Page<BiIdxConfDto>> pageQueryIdxConf(@RequestParam(defaultValue = "1") int currentPage,
                                                           @RequestParam(defaultValue = "10") int pageSize,
                                                           @RequestParam(required = false) String code,
                                                           @RequestParam(required = false) String name,
                                                           @RequestParam(required = false) String elementScene) {
        Page<BiIdxConfDto> biIdxConfDtoPage = biIdxConfBizService.pageQueryIdxConf(currentPage, pageSize, code, name, elementScene);
        return BaseResponse.ok(biIdxConfDtoPage);
    }

}
