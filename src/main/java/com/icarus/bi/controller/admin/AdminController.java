package com.icarus.bi.controller.admin;

import com.icarus.bi.common.rest.BaseResponse;
import com.icarus.bi.controller.BiCotTaskController;
import com.icarus.bi.entity.BiCotTask;
import com.icarus.bi.schedule.IBiCotTaskScheduleService;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 运维接口
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Tag(name = "问数-运维接口")
@Slf4j
@RestController
@Validated
@RequestMapping("/bi/admin")
public class AdminController {

    @Resource
    private BiCotTaskController cotTaskController;

    @Resource
    private IBiCotTaskScheduleService taskScheduleService;

    @ApiOperation(value = "手动执行指定任务")
    @PostMapping("/retry")
    public BaseResponse<Void> retryTask(@RequestParam @NotBlank(message = "ID不能为空") String id) {
        BaseResponse<BiCotTask> taskById = cotTaskController.getTaskById(id);
        BiCotTask task = taskById.getData();
        taskScheduleService.executeTaskAsync(task);
        return BaseResponse.ok(null);
    }

}