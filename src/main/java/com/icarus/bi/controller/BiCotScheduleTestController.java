package com.icarus.bi.controller;

import com.icarus.bi.common.rest.BaseResponse;
import com.icarus.bi.schedule.BiCotTaskScheduler;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * COT定时任务测试控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Tag(name = "COT定时任务测试控制器")
@Slf4j
@RestController
@RequestMapping("/biCotSchedule/test")
public class BiCotScheduleTestController {

    @Resource
    private BiCotTaskScheduler biCotTaskScheduler;

    @ApiOperation(value = "手动触发任务扫描")
    @PostMapping("/triggerScan")
    public BaseResponse<String> triggerTaskScan() {
        try {
            log.info("[手动触发任务扫描]开始");
            biCotTaskScheduler.scanAndExecuteTasks();
            log.info("[手动触发任务扫描]完成");
            return BaseResponse.ok("任务扫描触发成功");
        } catch (Exception e) {
            log.error("[手动触发任务扫描]失败, error:{}", e.getMessage(), e);
            return BaseResponse.serviceError("任务扫描触发失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "手动触发长时间运行任务清理")
    @PostMapping("/triggerCleanup")
    public BaseResponse<String> triggerTaskCleanup() {
        try {
            log.info("[手动触发任务清理]开始");
            biCotTaskScheduler.cleanupLongRunningTasks();
            log.info("[手动触发任务清理]完成");
            return BaseResponse.ok("任务清理触发成功");
        } catch (Exception e) {
            log.error("[手动触发任务清理]失败, error:{}", e.getMessage(), e);
            return BaseResponse.serviceError("任务清理触发失败: " + e.getMessage());
        }
    }
}
