package com.icarus.bi.controller;

import com.icarus.bi.common.rest.BaseResponse;
import com.icarus.bi.service.external.AgentCotService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * Agent COT接口测试控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Api(description = "Agent COT接口测试控制器")
@Slf4j
@RestController
@RequestMapping("/agentCot/test")
public class AgentCotTestController {

    @Resource
    private AgentCotService agentCotService;

    @ApiOperation(value = "测试COT要素抽取")
    @PostMapping("/extractElements")
    public BaseResponse<Map<String, Object>> testExtractElements(@RequestParam String canvasId,
                                                                 @RequestParam String extraAttrs) {
        try {
            log.info("[测试COT要素抽取]开始, canvasId:{}, extraAttrs:{}", canvasId, extraAttrs);
            
            // 调用抽取接口
            String extractResult = agentCotService.extractElements(canvasId, extraAttrs);
            
            // 解析结果
            Map<String, Object> elements = agentCotService.parseExtractResult(extractResult);
            
            log.info("[测试COT要素抽取]成功, 抽取要素数量:{}", elements.size());
            
            return BaseResponse.ok("抽取成功", elements);
            
        } catch (Exception e) {
            log.error("[测试COT要素抽取]失败, canvasId:{}, error:{}", canvasId, e.getMessage(), e);
            return BaseResponse.serviceError("抽取失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "测试extraAttrs解析")
    @PostMapping("/testParseExtraAttrs")
    public BaseResponse<String> testParseExtraAttrs(@RequestParam String extraAttrs) {
        try {
            log.info("[测试extraAttrs解析]开始, extraAttrs:{}", extraAttrs);

            // 使用反射调用私有方法进行测试
            java.lang.reflect.Method method = AgentCotService.class.getDeclaredMethod("extractMinioFileUrl", String.class);
            method.setAccessible(true);
            String fileUrl = (String) method.invoke(agentCotService, extraAttrs);

            log.info("[测试extraAttrs解析]成功, fileUrl:{}", fileUrl);

            return BaseResponse.ok("解析成功", fileUrl);

        } catch (Exception e) {
            log.error("[测试extraAttrs解析]失败, extraAttrs:{}, error:{}", extraAttrs, e.getMessage(), e);
            return BaseResponse.serviceError("解析失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "测试文件下载和大小验证")
    @PostMapping("/testFileDownload")
    public BaseResponse<Map<String, Object>> testFileDownload(@RequestParam String fileUrl) {
        try {
            log.info("[测试文件下载]开始, fileUrl:{}", fileUrl);

            // 使用反射调用私有方法进行测试
            java.lang.reflect.Method downloadMethod = AgentCotService.class.getDeclaredMethod("downloadFileFromUrl", String.class);
            downloadMethod.setAccessible(true);
            byte[] fileBytes = (byte[]) downloadMethod.invoke(agentCotService, fileUrl);

            // 计算文件大小
            double fileSizeMB = fileBytes.length / (1024.0 * 1024.0);

            Map<String, Object> result = new HashMap<>();
            result.put("fileUrl", fileUrl);
            result.put("fileSizeBytes", fileBytes.length);
            result.put("fileSizeMB", String.format("%.2f", fileSizeMB));
            result.put("maxSizeMB", "100");
            result.put("isWithinLimit", fileBytes.length <= (100 * 1024 * 1024));

            log.info("[测试文件下载]成功, fileUrl:{}, fileSize:{:.2f}MB", fileUrl, fileSizeMB);

            return BaseResponse.ok("下载成功", result);

        } catch (Exception e) {
            log.error("[测试文件下载]失败, fileUrl:{}, error:{}", fileUrl, e.getMessage(), e);

            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("fileUrl", fileUrl);
            errorResult.put("error", e.getMessage());
            errorResult.put("isFileSizeError", e.getMessage().contains("文件大小超过限制") || e.getMessage().contains("文件过大"));

            return BaseResponse.serviceError("下载失败: " + e.getMessage(), errorResult);
        }
    }
}
