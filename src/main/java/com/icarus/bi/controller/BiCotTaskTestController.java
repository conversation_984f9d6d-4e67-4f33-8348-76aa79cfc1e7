package com.icarus.bi.controller;

import com.icarus.bi.common.rest.BaseResponse;
import com.icarus.bi.dto.BiCotTaskErrorResponse;
import com.icarus.bi.entity.BiCotRule;
import com.icarus.bi.service.IBiCotRuleService;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * COT任务测试控制器 - 用于测试规则状态
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Tag(name = "COT任务测试控制器")
@Slf4j
@RestController
@RequestMapping("/biCotTask/test")
public class BiCotTaskTestController {

    @Resource
    private IBiCotRuleService biCotRuleService;

    @ApiOperation(value = "检查当前是否有启用的抽取规则")
    @GetMapping("/checkActiveRule")
    public BaseResponse<Object> checkActiveRule() {
        try {
            BiCotRule activeRule = biCotRuleService.getActiveRule();
            if (activeRule == null) {
                BiCotTaskErrorResponse errorResponse = BiCotTaskErrorResponse.ruleNotActive(
                        "当前没有启用的抽取规则");
                return BaseResponse.badRequest("未找到启用的抽取规则", errorResponse);
            }
            
            return BaseResponse.ok("找到启用的抽取规则", activeRule);
        } catch (Exception e) {
            log.error("[检查启用规则]失败, error:{}", e.getMessage(), e);
            BiCotTaskErrorResponse errorResponse = BiCotTaskErrorResponse.generalError(
                    "CHECK_RULE_FAILED", "检查规则状态失败: " + e.getMessage());
            return BaseResponse.serviceError("检查失败", errorResponse);
        }
    }

    @ApiOperation(value = "模拟创建任务时的规则检查")
    @PostMapping("/simulateTaskCreate")
    public BaseResponse<Object> simulateTaskCreate() {
        try {
            BiCotRule activeRule = biCotRuleService.getActiveRule();
            if (activeRule == null) {
                log.warn("[模拟创建任务]未找到启用的抽取规则");
                BiCotTaskErrorResponse errorResponse = BiCotTaskErrorResponse.ruleNotActive(
                        "未找到启用的抽取规则, 请先启用规则");
                return BaseResponse.badRequest("无法创建任务", errorResponse);
            }
            
            return BaseResponse.ok("可以创建任务", activeRule);
        } catch (Exception e) {
            log.error("[模拟创建任务]失败, error:{}", e.getMessage(), e);
            BiCotTaskErrorResponse errorResponse = BiCotTaskErrorResponse.generalError(
                    "SIMULATE_FAILED", "模拟失败: " + e.getMessage());
            return BaseResponse.serviceError("模拟失败", errorResponse);
        }
    }
}
