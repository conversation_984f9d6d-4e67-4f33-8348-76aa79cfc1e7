package com.icarus.bi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 审核状态枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Getter
@AllArgsConstructor
public enum AuditStatusEnum {

    /**
     * 待审核
     */
    UNDO("UNDO", "待审核"),

    /**
     * 已审核
     */
    AUDITED("AUDITED", "已审核");

    /**
     * 状态码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 审核状态枚举
     */
    public static AuditStatusEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (AuditStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否为有效的审核状态
     *
     * @param code 状态码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        return getByCode(code) != null;
    }

    /**
     * 判断是否为待审核状态
     *
     * @param code 状态码
     * @return 是否为待审核
     */
    public static boolean isUndo(String code) {
        return UNDO.getCode().equals(code);
    }

    /**
     * 判断是否为已审核状态
     *
     * @param code 状态码
     * @return 是否为已审核
     */
    public static boolean isAudited(String code) {
        return AUDITED.getCode().equals(code);
    }
}
