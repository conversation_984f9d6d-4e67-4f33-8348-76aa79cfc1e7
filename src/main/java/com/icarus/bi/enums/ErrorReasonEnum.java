package com.icarus.bi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 错误原因枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Getter
@AllArgsConstructor
public enum ErrorReasonEnum {

    /**
     * 参数错误
     */
    PARAM_ERROR("PARAM_ERROR", "参数错误"),

    /**
     * 参数为空
     */
    PARAM_NULL("PARAM_NULL", "参数为空"),

    /**
     * 参数格式错误
     */
    PARAM_FORMAT_ERROR("PARAM_FORMAT_ERROR", "参数格式错误"),

    /**
     * 数据不存在
     */
    DATA_NOT_FOUND("DATA_NOT_FOUND", "数据不存在"),

    /**
     * 数据已存在
     */
    DATA_ALREADY_EXISTS("DATA_ALREADY_EXISTS", "数据已存在"),

    /**
     * 数据库操作失败
     */
    DATABASE_ERROR("DATABASE_ERROR", "数据库操作失败"),

    /**
     * 网络连接失败
     */
    NETWORK_ERROR("NETWORK_ERROR", "网络连接失败"),

    /**
     * 权限不足
     */
    PERMISSION_DENIED("PERMISSION_DENIED", "权限不足"),

    /**
     * 认证失败
     */
    AUTH_FAILED("AUTH_FAILED", "认证失败"),

    /**
     * 业务逻辑错误
     */
    BUSINESS_ERROR("BUSINESS_ERROR", "业务逻辑错误"),

    /**
     * 系统内部错误
     */
    SYSTEM_ERROR("SYSTEM_ERROR", "系统内部错误"),

    /**
     * 文件操作失败
     */
    FILE_ERROR("FILE_ERROR", "文件操作失败"),

    /**
     * 文件不存在
     */
    FILE_NOT_FOUND("FILE_NOT_FOUND", "文件不存在"),

    /**
     * 文件格式错误
     */
    FILE_FORMAT_ERROR("FILE_FORMAT_ERROR", "文件格式错误"),

    /**
     * 服务调用失败
     */
    SERVICE_CALL_ERROR("SERVICE_CALL_ERROR", "服务调用失败"),

    /**
     * 超时错误
     */
    TIMEOUT_ERROR("TIMEOUT_ERROR", "操作超时"),

    /**
     * 配置错误
     */
    CONFIG_ERROR("CONFIG_ERROR", "配置错误"),

    /**
     * 数据解析错误
     */
    DATA_PARSE_ERROR("DATA_PARSE_ERROR", "数据解析错误"),

    /**
     * 未知错误
     */
    UNKNOWN_ERROR("UNKNOWN_ERROR", "未知错误");

    /**
     * 错误码
     */
    private final String code;

    /**
     * 错误描述
     */
    private final String description;

    /**
     * 根据错误码获取枚举
     *
     * @param code 错误码
     * @return 错误原因枚举
     */
    public static ErrorReasonEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ErrorReasonEnum error : values()) {
            if (error.getCode().equals(code)) {
                return error;
            }
        }
        return null;
    }

    /**
     * 判断是否为有效的错误码
     *
     * @param code 错误码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        return getByCode(code) != null;
    }

    /**
     * 判断是否为参数相关错误
     *
     * @param code 错误码
     * @return 是否为参数错误
     */
    public static boolean isParamError(String code) {
        return PARAM_ERROR.getCode().equals(code) || 
               PARAM_NULL.getCode().equals(code) || 
               PARAM_FORMAT_ERROR.getCode().equals(code);
    }

    /**
     * 判断是否为数据相关错误
     *
     * @param code 错误码
     * @return 是否为数据错误
     */
    public static boolean isDataError(String code) {
        return DATA_NOT_FOUND.getCode().equals(code) || 
               DATA_ALREADY_EXISTS.getCode().equals(code) ||
               DATA_PARSE_ERROR.getCode().equals(code);
    }

    /**
     * 判断是否为文件相关错误
     *
     * @param code 错误码
     * @return 是否为文件错误
     */
    public static boolean isFileError(String code) {
        return FILE_ERROR.getCode().equals(code) || 
               FILE_NOT_FOUND.getCode().equals(code) || 
               FILE_FORMAT_ERROR.getCode().equals(code);
    }

    /**
     * 判断是否为系统级错误
     *
     * @param code 错误码
     * @return 是否为系统错误
     */
    public static boolean isSystemError(String code) {
        return SYSTEM_ERROR.getCode().equals(code) || 
               DATABASE_ERROR.getCode().equals(code) || 
               NETWORK_ERROR.getCode().equals(code) ||
               SERVICE_CALL_ERROR.getCode().equals(code) ||
               TIMEOUT_ERROR.getCode().equals(code) ||
               CONFIG_ERROR.getCode().equals(code);
    }

    /**
     * 判断是否为权限相关错误
     *
     * @param code 错误码
     * @return 是否为权限错误
     */
    public static boolean isAuthError(String code) {
        return PERMISSION_DENIED.getCode().equals(code) || 
               AUTH_FAILED.getCode().equals(code);
    }
}
