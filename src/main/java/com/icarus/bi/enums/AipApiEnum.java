package com.icarus.bi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum AipApiEnum {

    aiSearch("/aip/api/cmtt/ai/search/aiSearch", "智能问答粗糙版本"),
    vector("/aip/api/cmtt/corpus/pushService/vector", "向量库操作节点"),
    runCrawler("/aip/api/cmtt/crawler/runCrawler/runWebCrawl", "调用爬虫"),
    getCrawlerTaskBySettingsId("/aip/crawler/data/tasks/settings", "查询爬虫任务"),
    fileSlice("/aip/api/cmtt/corpus/slice/file", "文件切片");

    private String path;

    private String description;

}
