package com.icarus.bi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 任务执行状态枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Getter
@AllArgsConstructor
public enum TaskStatusEnum {

    /**
     * 待启动
     */
    TODO("TODO", "待启动"),

    /**
     * 等待执行
     */
    WAITING("WAITING", "等待执行"),

    /**
     * COT进行中
     */
    COT_DOING("COT_DOING", "COT进行中"),

    /**
     * COT已完成
     */
    COT_DONE("COT_DONE", "COT已完成"),

    /**
     * IDX进行中
     */
    IDX_DOING("IDX_DOING", "IDX进行中"),

    /**
     * IDX已完成
     */
    IDX_DONE("IDX_DONE", "IDX已完成"),

    /**
     * 异常
     */
    ERROR("ERROR", "异常");

    /**
     * 状态码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 任务状态枚举
     */
    public static TaskStatusEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (TaskStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否为有效的任务状态
     *
     * @param code 状态码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        return getByCode(code) != null;
    }

    /**
     * 判断是否为待启动状态
     *
     * @param code 状态码
     * @return 是否为待启动
     */
    public static boolean isTodo(String code) {
        return TODO.getCode().equals(code);
    }

    /**
     * 判断是否为COT执行中状态
     *
     * @param code 状态码
     * @return 是否为COT执行中
     */
    public static boolean isCotDoing(String code) {
        return COT_DOING.getCode().equals(code);
    }

    /**
     * 判断是否为COT已完成状态
     *
     * @param code 状态码
     * @return 是否为COT已完成
     */
    public static boolean isCotDone(String code) {
        return COT_DONE.getCode().equals(code);
    }

    /**
     * 判断是否为IDX执行中状态
     *
     * @param code 状态码
     * @return 是否为IDX执行中
     */
    public static boolean isIdxDoing(String code) {
        return IDX_DOING.getCode().equals(code);
    }

    /**
     * 判断是否为IDX已完成状态
     *
     * @param code 状态码
     * @return 是否为IDX已完成
     */
    public static boolean isIdxDone(String code) {
        return IDX_DONE.getCode().equals(code);
    }

    /**
     * 判断是否为异常状态
     *
     * @param code 状态码
     * @return 是否为异常
     */
    public static boolean isError(String code) {
        return ERROR.getCode().equals(code);
    }

    /**
     * 判断是否为执行中状态（COT_DOING、COT_DONE 或 IDX_DOING）
     * 注意：COT_DONE也被认为是执行中状态，因为还需要进行IDX处理
     *
     * @param code 状态码
     * @return 是否为执行中
     */
    public static boolean isRunning(String code) {
        return isCotDoing(code) || isCotDone(code) || isIdxDoing(code);
    }

    /**
     * 获取所有执行中状态的代码列表
     * 包括：COT_DOING、COT_DONE、IDX_DOING
     *
     * @return 执行中状态代码列表
     */
    public static java.util.List<String> getRunningStatusCodes() {
        return java.util.Arrays.asList(
                COT_DOING.getCode(),
                COT_DONE.getCode(),
                IDX_DOING.getCode()
        );
    }

    /**
     * 判断是否为已完成状态（COT_DONE 或 IDX_DONE）
     *
     * @param code 状态码
     * @return 是否为已完成
     */
    public static boolean isCompleted(String code) {
        return isCotDone(code) || isIdxDone(code);
    }

    /**
     * 判断是否为最终状态（IDX_DONE 或 ERROR）
     *
     * @param code 状态码
     * @return 是否为最终状态
     */
    public static boolean isFinalStatus(String code) {
        return isIdxDone(code) || isError(code);
    }

    /**
     * 获取下一个状态
     *
     * @param currentCode 当前状态码
     * @return 下一个状态，如果没有下一个状态则返回null
     */
    public static TaskStatusEnum getNextStatus(String currentCode) {
        TaskStatusEnum current = getByCode(currentCode);
        if (current == null) {
            return null;
        }
        
        switch (current) {
            case TODO:
                return COT_DOING;
            case COT_DOING:
                return COT_DONE;
            case COT_DONE:
                return IDX_DOING;
            case IDX_DOING:
                return IDX_DONE;
            case IDX_DONE:
            case ERROR:
            default:
                return null; // 最终状态，没有下一个状态
        }
    }
}
