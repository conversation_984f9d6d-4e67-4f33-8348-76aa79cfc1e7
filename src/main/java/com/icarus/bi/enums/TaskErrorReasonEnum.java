package com.icarus.bi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 任务错误原因枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Getter
@AllArgsConstructor
public enum TaskErrorReasonEnum {

    /**
     * 任务参数错误
     */
    TASK_PARAM_ERROR("TASK_PARAM_ERROR", "任务参数错误"),

    /**
     * 任务配置缺失
     */
    TASK_CONFIG_MISSING("TASK_CONFIG_MISSING", "任务配置缺失"),

    /**
     * 任务已存在
     */
    TASK_ALREADY_EXISTS("TASK_ALREADY_EXISTS", "任务已存在"),

    /**
     * 任务不存在
     */
    TASK_NOT_FOUND("TASK_NOT_FOUND", "任务不存在"),

    /**
     * 任务状态异常
     */
    TASK_STATUS_ERROR("TASK_STATUS_ERROR", "任务状态异常"),

    /**
     * 任务执行超时
     */
    TASK_TIMEOUT("TASK_TIMEOUT", "任务执行超时"),

    /**
     * 任务执行失败
     */
    TASK_EXECUTION_FAILED("TASK_EXECUTION_FAILED", "任务执行失败"),

    /**
     * COT处理失败
     */
    COT_PROCESS_FAILED("COT_PROCESS_FAILED", "COT处理失败"),

    /**
     * IDX处理失败
     */
    IDX_PROCESS_FAILED("IDX_PROCESS_FAILED", "IDX处理失败"),

    /**
     * 数据源连接失败
     */
    DATASOURCE_CONNECTION_FAILED("DATASOURCE_CONNECTION_FAILED", "数据源连接失败"),

    /**
     * 数据抽取失败
     */
    DATA_EXTRACT_FAILED("DATA_EXTRACT_FAILED", "数据抽取失败"),

    /**
     * 数据转换失败
     */
    DATA_TRANSFORM_FAILED("DATA_TRANSFORM_FAILED", "数据转换失败"),

    /**
     * 数据加载失败
     */
    DATA_LOAD_FAILED("DATA_LOAD_FAILED", "数据加载失败"),

    /**
     * 规则引擎执行失败
     */
    RULE_ENGINE_FAILED("RULE_ENGINE_FAILED", "规则引擎执行失败"),

    /**
     * 规则未启用
     */
    RULE_NOT_ACTIVE("RULE_NOT_ACTIVE", "规则未启用"),

    /**
     * 调度器异常
     */
    SCHEDULER_ERROR("SCHEDULER_ERROR", "调度器异常"),

    /**
     * 资源不足
     */
    RESOURCE_INSUFFICIENT("RESOURCE_INSUFFICIENT", "资源不足"),

    /**
     * 依赖任务失败
     */
    DEPENDENCY_TASK_FAILED("DEPENDENCY_TASK_FAILED", "依赖任务失败"),

    /**
     * 网络连接失败
     */
    NETWORK_ERROR("NETWORK_ERROR", "网络连接失败"),

    /**
     * 系统内部错误
     */
    SYSTEM_ERROR("SYSTEM_ERROR", "系统内部错误"),

    /**
     * COT抽取数据缺少conf配置的主键值
     */
    COT_CONF_PRIMARY_KEY_MISSING("COT_CONF_PRIMARY_KEY_MISSING", "COT抽取数据缺少conf配置的主键值"),

    /**
     * 解析COT结果失败
     */
    PARSE_COT_RESULT_FAILED("PARSE_COT_RESULT_FAILED", "解析COT结果失败"),

    /**
     * 未知错误
     */
    UNKNOWN_ERROR("UNKNOWN_ERROR", "未知错误");

    /**
     * 错误码
     */
    private final String code;

    /**
     * 错误描述
     */
    private final String description;

    /**
     * 根据错误码获取枚举
     *
     * @param code 错误码
     * @return 任务错误原因枚举
     */
    public static TaskErrorReasonEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (TaskErrorReasonEnum error : values()) {
            if (error.getCode().equals(code)) {
                return error;
            }
        }
        return null;
    }

    /**
     * 判断是否为有效的错误码
     *
     * @param code 错误码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        return getByCode(code) != null;
    }

    /**
     * 判断是否为任务配置相关错误
     *
     * @param code 错误码
     * @return 是否为任务配置错误
     */
    public static boolean isTaskConfigError(String code) {
        return TASK_PARAM_ERROR.getCode().equals(code) || 
               TASK_CONFIG_MISSING.getCode().equals(code) || 
               TASK_STATUS_ERROR.getCode().equals(code);
    }

    /**
     * 判断是否为任务执行相关错误
     *
     * @param code 错误码
     * @return 是否为任务执行错误
     */
    public static boolean isTaskExecutionError(String code) {
        return TASK_EXECUTION_FAILED.getCode().equals(code) || 
               TASK_TIMEOUT.getCode().equals(code) ||
               COT_PROCESS_FAILED.getCode().equals(code) ||
               IDX_PROCESS_FAILED.getCode().equals(code);
    }

    /**
     * 判断是否为数据处理相关错误
     *
     * @param code 错误码
     * @return 是否为数据处理错误
     */
    public static boolean isDataProcessError(String code) {
        return DATA_EXTRACT_FAILED.getCode().equals(code) || 
               DATA_TRANSFORM_FAILED.getCode().equals(code) || 
               DATA_LOAD_FAILED.getCode().equals(code) ||
               DATASOURCE_CONNECTION_FAILED.getCode().equals(code);
    }

    /**
     * 判断是否为规则引擎相关错误
     *
     * @param code 错误码
     * @return 是否为规则引擎错误
     */
    public static boolean isRuleEngineError(String code) {
        return RULE_ENGINE_FAILED.getCode().equals(code) || 
               RULE_NOT_ACTIVE.getCode().equals(code);
    }

    /**
     * 判断是否为系统级错误
     *
     * @param code 错误码
     * @return 是否为系统错误
     */
    public static boolean isSystemError(String code) {
        return SYSTEM_ERROR.getCode().equals(code) || 
               SCHEDULER_ERROR.getCode().equals(code) || 
               NETWORK_ERROR.getCode().equals(code) ||
               RESOURCE_INSUFFICIENT.getCode().equals(code);
    }

    /**
     * 判断是否为依赖相关错误
     *
     * @param code 错误码
     * @return 是否为依赖错误
     */
    public static boolean isDependencyError(String code) {
        return DEPENDENCY_TASK_FAILED.getCode().equals(code);
    }

    /**
     * 判断是否为可重试的错误
     *
     * @param code 错误码
     * @return 是否可重试
     */
    public static boolean isRetryable(String code) {
        return TASK_TIMEOUT.getCode().equals(code) ||
               NETWORK_ERROR.getCode().equals(code) ||
               DATASOURCE_CONNECTION_FAILED.getCode().equals(code) ||
               RESOURCE_INSUFFICIENT.getCode().equals(code) ||
               SYSTEM_ERROR.getCode().equals(code);
    }
}
